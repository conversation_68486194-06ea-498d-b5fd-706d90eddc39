<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Mod_akun_default extends CI_Model
{
    var $table = 'akun_default';
    var $column_order = array('ad.nama_setting', 'coa.kode_akun', 'coa.nama_akun', 'ad.keterangan');
    var $column_search = array('ad.nama_setting', 'coa.kode_akun', 'coa.nama_akun', 'ad.keterangan');
    var $order = array('ad.nama_setting' => 'asc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('ad.*, coa.kode_akun, coa.nama_akun as nama_akun_chart');
        $this->db->from($this->table . ' ad');
        $this->db->join('chart_of_accounts coa', 'ad.id_akun = coa.id', 'left');
        
        $i = 0;
        foreach ($this->column_search as $item) {
            if($_POST['search']['value']) {
                if($i===0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                
                if(count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }
        
        if(isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if(isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    public function get_by_id($id)
    {
        $this->db->select('ad.*, coa.kode_akun, coa.nama_akun as nama_akun_chart');
        $this->db->from($this->table . ' ad');
        $this->db->join('chart_of_accounts coa', 'ad.id_akun = coa.id', 'left');
        $this->db->where('ad.id', $id);
        return $this->db->get()->row();
    }

    public function get_by_setting_name($nama_setting)
    {
        $this->db->select('ad.*, coa.kode_akun, coa.nama_akun as nama_akun_chart');
        $this->db->from($this->table . ' ad');
        $this->db->join('chart_of_accounts coa', 'ad.id_akun = coa.id', 'left');
        $this->db->where('ad.nama_setting', $nama_setting);
        return $this->db->get()->row();
    }

    public function save($data)
    {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    public function update($where, $data)
    {
        $this->db->update($this->table, $data, $where);
        return $this->db->affected_rows();
    }

    public function delete_by_id($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table);
        return $this->db->affected_rows();
    }

    public function check_nama_setting_exists($nama_setting, $id = null)
    {
        $this->db->where('nama_setting', $nama_setting);
        if($id !== null) {
            $this->db->where('id !=', $id);
        }
        $query = $this->db->get($this->table);
        return $query->num_rows() > 0;
    }

    public function get_all_chart_of_accounts()
    {
        $this->db->select('id, kode_akun, nama_akun');
        $this->db->where('is_active', 'Y');
        $this->db->order_by('kode_akun', 'ASC');
        return $this->db->get('chart_of_accounts')->result();
    }

    public function get_all()
    {
        $this->db->select('ad.*, coa.kode_akun, coa.nama_akun as nama_akun_chart');
        $this->db->from($this->table . ' ad');
        $this->db->join('chart_of_accounts coa', 'ad.id_akun = coa.id', 'left');
        $this->db->order_by('ad.nama_setting', 'ASC');
        return $this->db->get()->result();
    }

    public function get_default_account($setting_name)
    {
        $this->db->select('id_akun');
        $this->db->where('nama_setting', $setting_name);
        $result = $this->db->get($this->table)->row();
        
        return $result ? $result->id_akun : null;
    }
}
