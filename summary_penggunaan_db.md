# Summary Pengg<PERSON>an Table, <PERSON>, <PERSON><PERSON>, dan Stored Procedure

## 1. Tabel Utama yang Digunakan
Seluruh tabel utama aplikasi (transaksi, master, user, dsb) **terkonfirmasi digunakan** di source code, baik untuk proses CRUD, join, maupun tampilan data. Contoh tabel:
- barang, barang_masuk, barang_keluar, pembelian, penjualan, pelanggan, supplier, user, userlevel, dsb.

## 2. View Database
Dari seluruh view yang ada di database, **hanya dua view** yang ditemukan digunakan di source code:
- `v_pembelian_summary` (di `Mod_pembelian.php`)
- `v_stok_summary` (di `Mod_penyesuaian_stok.php`)

View lain seperti `v_barang_dengan_berat`, `v_barang_keluar_detail`, `v_pengiriman_detail`, dsb, **tidak ditemukan** digunakan di source code aplikasi.

## 3. Stored Procedure
- `sp_update_pembelian_status_after_penerimaan` digunakan di `Mod_penerimaan_pembelian.php`.
- `sp_update_pembelian_totals` digunakan di `Mod_pembelian.php`.
- `sp_update_berat_pengiriman_from_master` dan `sp_update_retur_totals` **tidak ditemukan** digunakan di source code.

## 4. Trigger
Semua trigger yang ada di database otomatis berjalan di level database (bukan dipanggil langsung dari aplikasi), sehingga tidak perlu ada pemanggilan eksplisit di source code.

## 5. Rangkuman
- **Tabel utama:** Semua digunakan aktif.
- **View:** Hanya sebagian kecil yang digunakan.
- **Stored Procedure:** Hanya sebagian kecil yang digunakan.
- **Trigger:** Semua aktif otomatis di database.

> Jika ingin membersihkan database, view/procedure yang tidak ditemukan di source code kemungkinan besar tidak digunakan aplikasi.

---

*Generated by GitHub Copilot, 2 Juli 2025.*
