<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ChartOfAccounts extends MY_Controller {

    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_chart_of_accounts');
    }

    public function index()
    {
        $this->template->load('layoutbackend', 'accounting/chart_of_accounts');
    }

    public function ajax_list()
    {
        $list = $this->Mod_chart_of_accounts->get_datatables();
        $data = array();
        $no = $_POST['start'];
        
        foreach ($list as $account) {
            $no++;
            $row = array();
            $row[] = $account->kode_akun;
            $row[] = str_repeat('&nbsp;&nbsp;&nbsp;', ($account->level - 1) * 2) . $account->nama_akun;
            $row[] = $account->tipe_akun;
            $row[] = $account->kategori_akun;
            $row[] = $account->saldo_normal;
            $row[] = $account->is_active == 'Y' ? '<span class="badge badge-success">Aktif</span>' : '<span class="badge badge-danger">Tidak Aktif</span>';
            
            // Action buttons
            $action = '';
            $action .= '<a class="btn btn-xs btn-outline-primary" href="javascript:void(0)" title="Edit" onclick="edit_account('.$account->id.')"><i class="fas fa-edit"></i></a> ';
            $action .= '<a class="btn btn-xs btn-outline-danger" href="javascript:void(0)" title="Delete" onclick="delete_account('.$account->id.')"><i class="fas fa-trash"></i></a>';
            $row[] = $action;
            
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_chart_of_accounts->count_all(),
            "recordsFiltered" => $this->Mod_chart_of_accounts->count_filtered(),
            "data" => $data,
        );
        
        echo json_encode($output);
    }

    public function add_form()
    {
        $data['parent_accounts'] = $this->Mod_chart_of_accounts->get_parent_accounts();
        $this->load->view('accounting/form_chart_of_accounts', $data);
    }

    public function edit_form($id)
    {
        $data['account'] = $this->Mod_chart_of_accounts->get_by_id($id);
        $data['parent_accounts'] = $this->Mod_chart_of_accounts->get_parent_accounts();
        $this->load->view('accounting/form_chart_of_accounts', $data);
    }

    public function insert()
    {
        $this->_validate();
        
        $parent_id = $this->input->post('parent_id') ?: null;
        $level = 1;
        
        if ($parent_id) {
            $parent = $this->Mod_chart_of_accounts->get_by_id($parent_id);
            $level = $parent->level + 1;
        }
        
        $save = array(
            'kode_akun' => $this->input->post('kode_akun'),
            'nama_akun' => $this->input->post('nama_akun'),
            'tipe_akun' => $this->input->post('tipe_akun'),
            'kategori_akun' => $this->input->post('kategori_akun'),
            'parent_id' => $parent_id,
            'level' => $level,
            'saldo_normal' => $this->input->post('saldo_normal'),
            'is_active' => $this->input->post('is_active'),
            'created_by' => $this->session->userdata('nama_user')
        );
        
        $this->Mod_chart_of_accounts->insert($save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $this->_validate();
        
        $id = $this->input->post('id');
        $parent_id = $this->input->post('parent_id') ?: null;
        $level = 1;
        
        if ($parent_id) {
            $parent = $this->Mod_chart_of_accounts->get_by_id($parent_id);
            $level = $parent->level + 1;
        }
        
        $save = array(
            'kode_akun' => $this->input->post('kode_akun'),
            'nama_akun' => $this->input->post('nama_akun'),
            'tipe_akun' => $this->input->post('tipe_akun'),
            'kategori_akun' => $this->input->post('kategori_akun'),
            'parent_id' => $parent_id,
            'level' => $level,
            'saldo_normal' => $this->input->post('saldo_normal'),
            'is_active' => $this->input->post('is_active'),
            'updated_by' => $this->session->userdata('nama_user')
        );
        
        $this->Mod_chart_of_accounts->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function delete()
    {
        $id = $this->input->post('id');
        
        // Check if account has children
        if ($this->Mod_chart_of_accounts->has_children($id)) {
            echo json_encode(array("status" => FALSE, "message" => "Tidak dapat menghapus akun yang memiliki sub akun"));
            return;
        }
        
        // Check if account is used in transactions
        if ($this->Mod_chart_of_accounts->is_used_in_transactions($id)) {
            echo json_encode(array("status" => FALSE, "message" => "Tidak dapat menghapus akun yang sudah digunakan dalam transaksi"));
            return;
        }
        
        $this->Mod_chart_of_accounts->delete($id);
        echo json_encode(array("status" => TRUE));
    }

    public function get_active_accounts()
    {
        $accounts = $this->Mod_chart_of_accounts->get_active_accounts();
        echo json_encode($accounts);
    }

    public function get_parent_accounts()
    {
        $parent_accounts = $this->Mod_chart_of_accounts->get_parent_accounts();
        $result = array();
        foreach ($parent_accounts as $acc) {
            $result[] = array(
                'id' => $acc->id,
                'kode_akun' => $acc->kode_akun,
                'nama_akun' => $acc->nama_akun,
                'level' => $acc->level,
                'tipe_akun' => $acc->tipe_akun,
                'kategori_akun' => $acc->kategori_akun
            );
        }
        echo json_encode($result);
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        if($this->input->post('kode_akun') == '') {
            $data['inputerror'][] = 'kode_akun';
            $data['error_string'][] = 'Kode akun harus diisi';
            $data['status'] = FALSE;
        }

        if($this->input->post('nama_akun') == '') {
            $data['inputerror'][] = 'nama_akun';
            $data['error_string'][] = 'Nama akun harus diisi';
            $data['status'] = FALSE;
        }

        if($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }
}
