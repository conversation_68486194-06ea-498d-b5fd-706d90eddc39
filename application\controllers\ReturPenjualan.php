<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Retur Penjualan
 * Mengatur retur penjualan dan detailnya
 * Terintegrasi dengan modul pengiriman dan faktur penjualan
 */
class ReturPenjualan extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_retur_penjualan', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'retur_penjualan/retur_penjualan', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $this->set_resource_limits();
        $list = $this->Mod_retur_penjualan->get_datatables();
        $data = array();
        foreach ($list as $retur) {
            $row = array();
            $row[] = $retur->nomor_retur;
            $row[] = date('d/m/Y', strtotime($retur->tanggal_retur));
            $row[] = $retur->nomor_faktur ?? '-';
            $row[] = $retur->nomor_pengiriman ?? '-';
            $row[] = $retur->nama_pelanggan . ' (' . $retur->kode_pelanggan . ')';
            
            // Status badge
            switch ($retur->status) {
                case 'draft':
                    $status_badge = '<span class="badge badge-warning">Draft</span>';
                    break;
                case 'diproses':
                    $status_badge = '<span class="badge badge-primary">Diproses</span>';
                    break;
                case 'selesai':
                    $status_badge = '<span class="badge badge-success">Selesai</span>';
                    break;
                case 'dibatalkan':
                    $status_badge = '<span class="badge badge-danger">Dibatalkan</span>';
                    break;
                default:
                    $status_badge = '<span class="badge badge-secondary">' . ucfirst($retur->status) . '</span>';
            }
            $row[] = $status_badge;
            
            $row[] = number_format($retur->total_item ?? 0, 0) . ' item';
            $row[] = number_format($retur->total_qty ?? 0, 0);
            $row[] = 'Rp ' . number_format($retur->total_nilai ?? 0, 0);
            
            // Action buttons
            $actions = '';
            if ($retur->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $retur->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $retur->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success process" href="javascript:void(0)" title="Proses" onclick="updateStatus(' . $retur->id . ', \'diproses\')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $retur->id . ')"><i class="fas fa-trash"></i></a>';
            } else if ($retur->status == 'diproses') {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $retur->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success complete" href="javascript:void(0)" title="Selesai" onclick="updateStatus(' . $retur->id . ', \'selesai\')"><i class="fas fa-check-double"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printRetur(' . $retur->id . ')"><i class="fas fa-print"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $retur->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printRetur(' . $retur->id . ')"><i class="fas fa-print"></i></a>';
            }

            $row[] = $actions;
            
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_retur_penjualan->count_all(),
            "recordsFiltered" => $this->Mod_retur_penjualan->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    // Method untuk form input modal
    public function form_input()
    {
        // Load dropdown data untuk form
        $data['pelanggan_list'] = $this->Mod_retur_penjualan->get_pelanggan_aktif();
        $data['faktur_list'] = $this->Mod_retur_penjualan->get_faktur_aktif();
        $data['pengiriman_list'] = $this->Mod_retur_penjualan->get_pengiriman_aktif();
        $data['nomor_retur'] = $this->Mod_retur_penjualan->generate_nomor_retur();
        $this->load->view('retur_penjualan/form_input', $data);
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_retur_penjualan->generate_nomor_retur();
        echo json_encode(array('nomor' => $nomor));
    }

    public function get_faktur_info()
    {
        $id = $this->input->post('id');
        $faktur = $this->Mod_retur_penjualan->get_faktur_by_id($id);

        if ($faktur) {
            echo json_encode(array('status' => true, 'data' => $faktur));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Data faktur tidak ditemukan'));
        }
    }

    public function get_pengiriman_info()
    {
        $id = $this->input->post('id');
        $pengiriman = $this->Mod_retur_penjualan->get_pengiriman_by_id($id);

        if ($pengiriman) {
            echo json_encode(array('status' => true, 'data' => $pengiriman));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Data pengiriman tidak ditemukan'));
        }
    }

    public function get_faktur_items()
    {
        $id = $this->input->post('id');
        $id_retur = $this->input->post('id_retur');
        $items = $this->Mod_retur_penjualan->get_items_from_faktur($id, $id_retur);

        if ($items) {
            echo json_encode(array('status' => true, 'data' => $items));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Item faktur tidak ditemukan atau sudah diretur semua'));
        }
    }

    public function get_pengiriman_items()
    {
        $id = $this->input->post('id');
        $id_retur = $this->input->post('id_retur');
        $items = $this->Mod_retur_penjualan->get_items_for_return($id, $id_retur);

        if ($items) {
            echo json_encode(array('status' => true, 'data' => $items));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Item pengiriman tidak ditemukan atau sudah diretur semua'));
        }
    }

    public function form_detail_item()
    {
        $id_retur = $this->input->post('id_retur');
        $id_faktur = $this->input->post('id_faktur');
        $id_pengiriman = $this->input->post('id_pengiriman');
        $sumber = $this->input->post('sumber');
        
        // Get retur data if id_retur is provided
        if ($id_retur) {
            $retur = $this->Mod_retur_penjualan->get_by_id($id_retur);
            if ($retur) {
                $id_faktur = $retur->id_faktur;
                $id_pengiriman = $retur->id_pengiriman;
                $sumber = $id_faktur ? 'faktur' : 'pengiriman';
            }
        }
        
        // Get items based on source
        $data['items'] = [];
        $data['existing_details'] = [];
        
        // Jika edit mode, ambil detail item yang sudah ada
        if ($id_retur) {
            $data['existing_details'] = $this->Mod_retur_penjualan->get_detail_by_retur_id($id_retur);
        }
        
        if ($sumber == 'faktur' && $id_faktur) {
            try {
                $available_items = $this->Mod_retur_penjualan->get_items_from_faktur($id_faktur, $id_retur);
                
                // Gabungkan item yang tersedia dengan item yang sudah diretur
                $data['items'] = $this->merge_items_with_existing($available_items, $data['existing_details'], 'faktur');
                
                // Jika tidak ada item, tampilkan pesan
                if (empty($data['items'])) {
                    log_message('error', 'Tidak ada item yang tersedia untuk diretur dari faktur ID: ' . $id_faktur);
                }
            } catch (Exception $e) {
                log_message('error', 'Error saat mengambil item faktur: ' . $e->getMessage());
            }
        } else if ($sumber == 'pengiriman' && $id_pengiriman) {
            try {
                $available_items = $this->Mod_retur_penjualan->get_items_for_return($id_pengiriman, $id_retur);
                
                // Gabungkan item yang tersedia dengan item yang sudah diretur
                $data['items'] = $this->merge_items_with_existing($available_items, $data['existing_details'], 'pengiriman');
                
                // Jika tidak ada item, tampilkan pesan
                if (empty($data['items'])) {
                    log_message('error', 'Tidak ada item yang tersedia untuk diretur dari pengiriman ID: ' . $id_pengiriman);
                }
            } catch (Exception $e) {
                log_message('error', 'Error saat mengambil item pengiriman: ' . $e->getMessage());
            }
        }
        
        $data['id_retur'] = $id_retur;
        $data['id_faktur'] = $id_faktur;
        $data['id_pengiriman'] = $id_pengiriman;
        $data['sumber'] = $sumber;
        $data['gudang_list'] = $this->Mod_retur_penjualan->get_gudang_aktif();
        
        $this->load->view('retur_penjualan/form_detail_item', $data);
    }
    
    /**
     * Menggabungkan item yang tersedia dengan item yang sudah diretur untuk mode edit
     */
    private function merge_items_with_existing($available_items, $existing_details, $sumber)
    {
        $merged_items = [];
        $existing_item_keys = [];
        
        // Buat array key untuk item yang sudah diretur
        foreach ($existing_details as $detail) {
            $key = $detail->id_barang . '_' . $detail->id_gudang;
            $existing_item_keys[$key] = $detail;
        }
        
        // Tambahkan item yang tersedia
        foreach ($available_items as $item) {
            $key = $item->id_barang . '_' . $item->id_gudang;
            
            // Jika item sudah ada di retur, gunakan data dari retur
            if (isset($existing_item_keys[$key])) {
                $existing = $existing_item_keys[$key];
                
                // Merge data item dengan data retur yang sudah ada
                $item->qty_retur_existing = $existing->qty_retur;
                $item->kondisi_existing = $existing->kondisi_barang;
                $item->harga_satuan_existing = $existing->harga_satuan;
                $item->keterangan_existing = $existing->keterangan;
                
                // Update qty tersedia dengan mengurangi qty yang sudah diretur dari retur lain
                // Qty dari retur yang sedang diedit tidak dihitung sebagai sudah diretur
                if ($sumber == 'faktur') {
                    $item->qty_sudah_diretur = max(0, $item->qty_sudah_diretur - $existing->qty_retur);
                } else {
                    $item->qty_sudah_diretur = max(0, $item->qty_sudah_diretur - $existing->qty_retur);
                }
                
                unset($existing_item_keys[$key]);
            }
            
            $merged_items[] = $item;
        }
        
        // Tambahkan item yang sudah diretur tapi tidak ada di available items
        foreach ($existing_item_keys as $detail) {
            $item = new stdClass();
            $item->id_barang = $detail->id_barang;
            $item->id_gudang = $detail->id_gudang;
            $item->kode_barang = $detail->kode_barang;
            $item->nama_barang = $detail->nama_barang;
            $item->merk = $detail->merk;
            $item->tipe = $detail->tipe;
            $item->nama_satuan = $detail->nama_satuan;
            $item->nama_gudang = $detail->nama_gudang;
            
            if ($sumber == 'faktur') {
                $item->qty_faktur = $detail->qty_retur;
                $item->qty_sudah_diretur = 0;
            } else {
                $item->qty_dikirim = $detail->qty_retur;
                $item->qty_sudah_diretur = 0;
            }
            
            $item->harga_satuan = $detail->harga_satuan;
            $item->qty_retur_existing = $detail->qty_retur;
            $item->kondisi_existing = $detail->kondisi_barang;
            $item->harga_satuan_existing = $detail->harga_satuan;
            $item->keterangan_existing = $detail->keterangan;
            
            $merged_items[] = $item;
        }
        
        return $merged_items;
    }
    
    public function get_warehouses_with_stock()
    {
        $id_barang = $this->input->post('id_barang');
        
        // Get warehouses with stock
        $warehouses = $this->Mod_retur_penjualan->get_warehouses_with_stock($id_barang);
        
        if ($warehouses) {
            echo json_encode(array('status' => true, 'data' => $warehouses));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Tidak ada gudang dengan stok tersedia untuk barang ini'));
        }
    }

    public function edit($id)
    {
        $data = $this->Mod_retur_penjualan->get_by_id($id);
        echo json_encode($data);
    }

    public function detail_modal($id)
    {
        $data['retur'] = $this->Mod_retur_penjualan->get_by_id($id);
        $data['detail'] = $this->Mod_retur_penjualan->get_detail_by_retur_id($id);
        $this->load->view('retur_penjualan/detail_modal', $data);
    }

    public function insert()
    {
        $this->_validate();

        $data = array(
            'nomor_retur' => $this->input->post('nomor_retur'),
            'tanggal_retur' => $this->input->post('tanggal_retur'),
            'id_faktur' => $this->input->post('id_faktur') ?: null,
            'id_pengiriman' => $this->input->post('id_pengiriman') ?: null,
            'id_pelanggan' => $this->input->post('id_pelanggan'),
            'alasan_retur' => $this->input->post('alasan_retur'),
            'status' => 'draft',
            'keterangan' => $this->input->post('keterangan'),
            'created_by' => $this->session->userdata('id_user'),
            'created_at' => date('Y-m-d H:i:s')
        );

        $insert = $this->Mod_retur_penjualan->insert($this->Mod_retur_penjualan->table, $data);

        // Jika ada detail item yang dikirimkan
        if ($this->input->post('id_barang')) {
            $id_barang = $this->input->post('id_barang');
            $id_gudang = $this->input->post('id_gudang');
            $qty_retur = $this->input->post('qty_retur');
            $kondisi_barang = $this->input->post('kondisi_barang');
            $harga_satuan = $this->input->post('harga_satuan');
            $keterangan_item = $this->input->post('keterangan_item');

            for ($i = 0; $i < count($id_barang); $i++) {
                if ($id_barang[$i] && $qty_retur[$i] > 0) {
                    $detail_data = array(
                        'id_retur' => $insert,
                        'id_barang' => $id_barang[$i],
                        'id_gudang' => $id_gudang[$i],
                        'qty_retur' => $qty_retur[$i],
                        'kondisi_barang' => $kondisi_barang[$i],
                        'harga_satuan' => $harga_satuan[$i],
                        'keterangan' => $keterangan_item[$i],
                        'created_at' => date('Y-m-d H:i:s')
                    );
                    $this->Mod_retur_penjualan->save_detail($detail_data);
                }
            }
        }

        echo json_encode(array("status" => TRUE, "message" => "Data retur penjualan berhasil disimpan"));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $data = array(
            'nomor_retur' => $this->input->post('nomor_retur'),
            'tanggal_retur' => $this->input->post('tanggal_retur'),
            'id_faktur' => $this->input->post('id_faktur') ?: null,
            'id_pengiriman' => $this->input->post('id_pengiriman') ?: null,
            'id_pelanggan' => $this->input->post('id_pelanggan'),
            'alasan_retur' => $this->input->post('alasan_retur'),
            'keterangan' => $this->input->post('keterangan'),
            'updated_by' => $this->session->userdata('id_user'),
            'updated_at' => date('Y-m-d H:i:s')
        );

        $this->Mod_retur_penjualan->update($id, $data);

        // Hapus detail lama dan insert yang baru
        $this->Mod_retur_penjualan->delete_detail_by_retur($id);

        // Jika ada detail item yang dikirimkan
        if ($this->input->post('id_barang')) {
            $id_barang = $this->input->post('id_barang');
            $id_gudang = $this->input->post('id_gudang');
            $qty_retur = $this->input->post('qty_retur');
            $kondisi_barang = $this->input->post('kondisi_barang');
            $harga_satuan = $this->input->post('harga_satuan');
            $keterangan_item = $this->input->post('keterangan_item');

            for ($i = 0; $i < count($id_barang); $i++) {
                if ($id_barang[$i] && $qty_retur[$i] > 0) {
                    $detail_data = array(
                        'id_retur' => $id,
                        'id_barang' => $id_barang[$i],
                        'id_gudang' => $id_gudang[$i],
                        'qty_retur' => $qty_retur[$i],
                        'kondisi_barang' => $kondisi_barang[$i],
                        'harga_satuan' => $harga_satuan[$i],
                        'keterangan' => $keterangan_item[$i],
                        'created_at' => date('Y-m-d H:i:s')
                    );
                    $this->Mod_retur_penjualan->save_detail($detail_data);
                }
            }
        }

        echo json_encode(array("status" => TRUE, "message" => "Data retur penjualan berhasil diupdate"));
    }

    public function delete()
    {
        $id = $this->input->post('id');
        
        // Cek status retur
        $retur = $this->Mod_retur_penjualan->get_by_id($id);
        if ($retur->status != 'draft') {
            echo json_encode(array("status" => "error", "message" => "Retur dengan status selain draft tidak dapat dihapus"));
            return;
        }
        
        // Hapus detail terlebih dahulu
        $this->Mod_retur_penjualan->delete_detail_by_retur($id);
        
        // Hapus header
        $this->Mod_retur_penjualan->delete($id, $this->Mod_retur_penjualan->table);
        
        echo json_encode(array("status" => "success", "message" => "Data retur penjualan berhasil dihapus"));
    }

    public function update_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        
        // Validasi status
        $valid_statuses = ['draft', 'diproses', 'selesai', 'dibatalkan'];
        if (!in_array($status, $valid_statuses)) {
            echo json_encode(array("status" => "error", "message" => "Status tidak valid"));
            return;
        }
        
        // Cek apakah retur memiliki detail
        $detail = $this->Mod_retur_penjualan->get_detail_by_retur_id($id);
        if (empty($detail) && $status != 'dibatalkan') {
            echo json_encode(array("status" => "error", "message" => "Retur tidak memiliki detail item"));
            return;
        }
        
        // Update status
        $data = array(
            'status' => $status,
            'updated_by' => $this->session->userdata('id_user'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->Mod_retur_penjualan->update($id, $data);
        
        // Jika status selesai, maka update stok
        if ($status == 'selesai') {
            // Proses update stok akan dilakukan di trigger database
            // atau bisa ditambahkan logika disini
        }
        
        echo json_encode(array("status" => "success", "message" => "Status retur berhasil diupdate"));
    }

    /**
     * Fungsi untuk mencetak retur penjualan
     * Alias untuk print_retur untuk kompatibilitas URL
     */
    public function print($id)
    {
        $this->print_retur($id);
    }

    public function print_retur($id)
    {
        $data['retur'] = $this->Mod_retur_penjualan->get_by_id($id);
        $data['detail'] = $this->Mod_retur_penjualan->get_detail_by_retur_id($id);
        $this->load->view('retur_penjualan/print_retur', $data);
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        if ($this->input->post('nomor_retur') == '') {
            $data['inputerror'][] = 'nomor_retur';
            $data['error_string'][] = 'Nomor Retur harus diisi';
            $data['status'] = FALSE;
        }

        if ($this->input->post('tanggal_retur') == '') {
            $data['inputerror'][] = 'tanggal_retur';
            $data['error_string'][] = 'Tanggal Retur harus diisi';
            $data['status'] = FALSE;
        }

        if ($this->input->post('id_pelanggan') == '') {
            $data['inputerror'][] = 'id_pelanggan';
            $data['error_string'][] = 'Pelanggan harus dipilih';
            $data['status'] = FALSE;
        }

        // Validasi sumber retur (harus ada salah satu)
        if ($this->input->post('id_faktur') == '' && $this->input->post('id_pengiriman') == '') {
            $data['inputerror'][] = 'id_faktur';
            $data['inputerror'][] = 'id_pengiriman';
            $data['error_string'][] = 'Pilih salah satu sumber retur (Faktur atau Pengiriman)';
            $data['error_string'][] = 'Pilih salah satu sumber retur (Faktur atau Pengiriman)';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }
}