<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller <PERSON><PERSON><PERSON><PERSON>
 * Mengatur penerimaan pembelian dan detailnya
 * Terintegrasi dengan modul pembelian dan barang masuk
 */
class PenerimaanPembelian extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_penerimaan_pembelian', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'penerimaan/penerimaan_pembelian', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $this->set_resource_limits();
        $list = $this->Mod_penerimaan_pembelian->get_datatables();
        $data = array();
        foreach ($list as $penerimaan) {
            $row = array();
            $row[] = $penerimaan->nomor_penerimaan;
            $row[] = date('d/m/Y', strtotime($penerimaan->tanggal_penerimaan));
            $row[] = $penerimaan->nomor_pembelian;
            $row[] = $penerimaan->nama_supplier . ' (' . $penerimaan->kode_supplier . ')';
            
            // Status badge
            switch ($penerimaan->status) {
                case 'draft':
                    $status_badge = '<span class="badge badge-warning">Draft</span>';
                    break;
                case 'diterima':
                    $status_badge = '<span class="badge badge-success">Diterima</span>';
                    break;
                case 'selesai':
                    $status_badge = '<span class="badge badge-info">Selesai</span>';
                    break;
                case 'dibatalkan':
                    $status_badge = '<span class="badge badge-danger">Dibatalkan</span>';
                    break;
                default:
                    $status_badge = '<span class="badge badge-secondary">' . ucfirst($penerimaan->status) . '</span>';
            }
            $row[] = $status_badge;
            
            $row[] = number_format($penerimaan->total_item ?? 0, 0) . ' item';
            $row[] = number_format($penerimaan->total_qty ?? 0, 0);
            
            // Penerima
            $row[] = $penerimaan->penerima ? $penerimaan->penerima : '-';
            
            // Tanggal diterima
            $row[] = $penerimaan->tanggal_diterima ? date('d/m/Y H:i', strtotime($penerimaan->tanggal_diterima)) : '-';
            
            // Action buttons
            $actions = '';
            if ($penerimaan->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $penerimaan->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $penerimaan->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success process" href="javascript:void(0)" title="Terima" onclick="updateStatus(' . $penerimaan->id . ', \'diterima\')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $penerimaan->id . ')"><i class="fas fa-trash"></i></a>';
            } else if ($penerimaan->status == 'diterima') {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $penerimaan->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success complete" href="javascript:void(0)" title="Selesai" onclick="updateStatus(' . $penerimaan->id . ', \'selesai\')"><i class="fas fa-check-double"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printPenerimaan(' . $penerimaan->id . ')"><i class="fas fa-print"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $penerimaan->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printPenerimaan(' . $penerimaan->id . ')"><i class="fas fa-print"></i></a>';
            }

            $row[] = $actions;
            
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_penerimaan_pembelian->count_all(),
            "recordsFiltered" => $this->Mod_penerimaan_pembelian->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    // Method untuk form input modal
    public function form_input()
    {
        // Load dropdown data untuk form
        $data['supplier_list'] = $this->Mod_penerimaan_pembelian->get_supplier_aktif();
        $data['pembelian_list'] = $this->Mod_penerimaan_pembelian->get_pembelian_aktif();
        $data['nomor_penerimaan'] = $this->Mod_penerimaan_pembelian->generate_nomor_penerimaan();
        $this->load->view('penerimaan/form_input', $data);
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_penerimaan_pembelian->generate_nomor_penerimaan();
        echo json_encode(array('nomor' => $nomor));
    }

    public function get_pembelian_info()
    {
        $id = $this->input->post('id');
        $pembelian = $this->Mod_penerimaan_pembelian->get_pembelian_by_id($id);

        if ($pembelian) {
            echo json_encode(array('status' => true, 'data' => $pembelian));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Data pembelian tidak ditemukan'));
        }
    }

    public function get_pembelian_items()
    {
        $id = $this->input->post('id');
        $items = $this->Mod_penerimaan_pembelian->get_pembelian_detail($id);

        if ($items) {
            echo json_encode(array('status' => true, 'data' => $items));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Item pembelian tidak ditemukan'));
        }
    }

    public function form_detail_item()
    {
        $id_penerimaan = $this->input->post('id_penerimaan');
        $id_pembelian = $this->input->post('id_pembelian');
        
        // Get penerimaan data if id_penerimaan is provided
        if ($id_penerimaan) {
            $penerimaan = $this->Mod_penerimaan_pembelian->get_by_id($id_penerimaan);
            if ($penerimaan) {
                $id_pembelian = $penerimaan->id_pembelian;
            }
        }
        
        // Periksa detail pembelian terlebih dahulu
        $pembelian_detail = [];
        if ($id_pembelian) {
            $pembelian_detail = $this->Mod_penerimaan_pembelian->get_pembelian_detail($id_pembelian);
            log_message('info', 'Pembelian ID: ' . $id_pembelian . ' memiliki ' . count($pembelian_detail) . ' item');
            
            // Jika tidak ada item dalam pembelian
            if (empty($pembelian_detail)) {
                $data['purchased_items'] = [];
                $data['error_message'] = 'Pembelian ini tidak memiliki item. Silakan tambahkan item pada pembelian terlebih dahulu.';
                $data['gudang_list'] = $this->Mod_penerimaan_pembelian->get_gudang_aktif();
                $data['id_penerimaan'] = $id_penerimaan;
                $data['id_pembelian'] = $id_pembelian;
                $this->load->view('penerimaan/form_detail_item', $data);
                return;
            }
        }
        
        // Periksa apakah pembelian memiliki item yang belum diterima sepenuhnya
        $has_unreceived_items = false;
        if ($id_pembelian) {
            $has_unreceived_items = $this->Mod_penerimaan_pembelian->check_pembelian_has_unreceived_items($id_pembelian);
            log_message('info', 'Pembelian ID: ' . $id_pembelian . ' has_unreceived_items: ' . ($has_unreceived_items ? 'true' : 'false'));
        }
        
        // Get purchased items with remaining quantities if id_pembelian is provided
        $data['purchased_items'] = [];
        if ($id_pembelian && $has_unreceived_items) {
            try {
                $data['purchased_items'] = $this->Mod_penerimaan_pembelian->get_pembelian_items_for_receiving($id_pembelian, $id_penerimaan);
                
                // Jika tidak ada item yang dikembalikan, tambahkan pesan khusus
                if (empty($data['purchased_items'])) {
                    $data['error_message'] = 'Semua item dalam pembelian ini sudah diterima sepenuhnya.';
                }
            } catch (Exception $e) {
                log_message('error', 'Error in form_detail_item: ' . $e->getMessage());
                $data['error_message'] = 'Terjadi kesalahan saat memuat data item.';
            }
        } elseif ($id_pembelian && !$has_unreceived_items && !empty($pembelian_detail)) {
            $data['error_message'] = 'Semua item dalam pembelian ini sudah diterima sepenuhnya.';
        }
        
        // Load gudang dropdown
        try {
            $data['gudang_list'] = $this->Mod_penerimaan_pembelian->get_gudang_aktif();
        } catch (Exception $e) {
            log_message('error', 'Error loading gudang list: ' . $e->getMessage());
            $data['gudang_list'] = [];
        }
        
        $data['id_penerimaan'] = $id_penerimaan;
        $data['id_pembelian'] = $id_pembelian;
        
        $this->load->view('penerimaan/form_detail_item', $data);
    }

    public function edit($id)
    {
        $data = $this->Mod_penerimaan_pembelian->get_by_id($id);
        echo json_encode($data);
    }

    public function detail_modal($id)
    {
        $data['penerimaan'] = $this->Mod_penerimaan_pembelian->get_by_id($id);
        $data['detail_items'] = $this->Mod_penerimaan_pembelian->get_detail_by_penerimaan_id($id);
        $this->load->view('penerimaan/detail_modal', $data);
    }

    public function insert()
    {
        $this->_validate();
        
        $data = array(
            'nomor_penerimaan' => $this->input->post('nomor_penerimaan'),
            'tanggal_penerimaan' => $this->input->post('tanggal_penerimaan'),
            'id_pembelian' => $this->input->post('id_pembelian'),
            'id_supplier' => $this->input->post('id_supplier'),
            'status' => 'draft',
            'keterangan' => $this->input->post('keterangan'),
            'created_by' => $this->session->userdata('id_user'),
            'created_at' => date('Y-m-d H:i:s')
        );
        
        $insert = $this->Mod_penerimaan_pembelian->insert('penerimaan_pembelian', $data);
        
        if ($insert) {
            // Process detail items if any
            $id_pembelian_detail = $this->input->post('id_pembelian_detail');
            $id_barang = $this->input->post('id_barang');
            $id_gudang = $this->input->post('id_gudang');
            $qty_pembelian = $this->input->post('qty_pembelian');
            $qty_diterima = $this->input->post('qty_diterima');
            $qty_ditolak = $this->input->post('qty_ditolak');
            $alasan_penolakan = $this->input->post('alasan_penolakan');
            $keterangan_item = $this->input->post('keterangan_item');
            
            if ($id_pembelian_detail && is_array($id_pembelian_detail)) {
                for ($i = 0; $i < count($id_pembelian_detail); $i++) {
                    if (isset($qty_diterima[$i]) && $qty_diterima[$i] > 0) {
                        $detail_data = array(
                            'id_penerimaan' => $insert,
                            'id_pembelian_detail' => $id_pembelian_detail[$i],
                            'id_barang' => $id_barang[$i],
                            'id_gudang' => $id_gudang[$i],
                            'qty_pembelian' => $qty_pembelian[$i],
                            'qty_diterima' => $qty_diterima[$i],
                            'qty_ditolak' => $qty_ditolak[$i] ?? 0,
                            'alasan_penolakan' => $alasan_penolakan[$i] ?? null,
                            'keterangan' => $keterangan_item[$i] ?? null,
                            'created_at' => date('Y-m-d H:i:s')
                        );
                        
                        $this->Mod_penerimaan_pembelian->save_detail($detail_data);
                    }
                }
            }
            
            // Update total penerimaan
            $this->Mod_penerimaan_pembelian->update_total_penerimaan($insert);
            
            echo json_encode(array("status" => true, "message" => "Penerimaan pembelian berhasil disimpan"));
        } else {
            echo json_encode(array("status" => false, "message" => "Gagal menyimpan penerimaan pembelian"));
        }
    }

    public function update()
    {
        $this->_validate();
        
        $id = $this->input->post('id');
        
        $data = array(
            'nomor_penerimaan' => $this->input->post('nomor_penerimaan'),
            'tanggal_penerimaan' => $this->input->post('tanggal_penerimaan'),
            'id_pembelian' => $this->input->post('id_pembelian'),
            'id_supplier' => $this->input->post('id_supplier'),
            'keterangan' => $this->input->post('keterangan'),
            'updated_by' => $this->session->userdata('id_user'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $update = $this->Mod_penerimaan_pembelian->update($id, $data);
        
        // Delete existing detail items
        $this->Mod_penerimaan_pembelian->delete_detail_by_penerimaan($id);
        
        // Process detail items if any
        $id_pembelian_detail = $this->input->post('id_pembelian_detail');
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $qty_pembelian = $this->input->post('qty_pembelian');
        $qty_diterima = $this->input->post('qty_diterima');
        $qty_ditolak = $this->input->post('qty_ditolak');
        $alasan_penolakan = $this->input->post('alasan_penolakan');
        $keterangan_item = $this->input->post('keterangan_item');
        
        if ($id_pembelian_detail && is_array($id_pembelian_detail)) {
            for ($i = 0; $i < count($id_pembelian_detail); $i++) {
                if (isset($qty_diterima[$i]) && $qty_diterima[$i] > 0) {
                    $detail_data = array(
                        'id_penerimaan' => $id,
                        'id_pembelian_detail' => $id_pembelian_detail[$i],
                        'id_barang' => $id_barang[$i],
                        'id_gudang' => $id_gudang[$i],
                        'qty_pembelian' => $qty_pembelian[$i],
                        'qty_diterima' => $qty_diterima[$i],
                        'qty_ditolak' => $qty_ditolak[$i] ?? 0,
                        'alasan_penolakan' => $alasan_penolakan[$i] ?? null,
                        'keterangan' => $keterangan_item[$i] ?? null,
                        'created_at' => date('Y-m-d H:i:s')
                    );
                    
                    $this->Mod_penerimaan_pembelian->save_detail($detail_data);
                }
            }
        }
        
        // Update total penerimaan
        $this->Mod_penerimaan_pembelian->update_total_penerimaan($id);
        
        echo json_encode(array("status" => true, "message" => "Penerimaan pembelian berhasil diupdate"));
    }

    public function delete()
    {
        $id = $this->input->post('id');
        
        // Check if penerimaan can be deleted (only draft status)
        $penerimaan = $this->Mod_penerimaan_pembelian->get_by_id($id);
        if (!$penerimaan || $penerimaan->status != 'draft') {
            echo json_encode(array("status" => false, "message" => "Penerimaan pembelian tidak dapat dihapus karena status bukan draft"));
            return;
        }
        
        // Delete detail first
        $this->Mod_penerimaan_pembelian->delete_detail_by_penerimaan($id);
        
        // Delete header
        $delete = $this->Mod_penerimaan_pembelian->delete($id, 'penerimaan_pembelian');
        
        if ($delete) {
            echo json_encode(array("status" => "success", "message" => "Penerimaan pembelian berhasil dihapus"));
        } else {
            echo json_encode(array("status" => "error", "message" => "Gagal menghapus penerimaan pembelian"));
        }
    }

    public function update_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        
        // Check if penerimaan exists
        $penerimaan = $this->Mod_penerimaan_pembelian->get_by_id($id);
        if (!$penerimaan) {
            echo json_encode(array("status" => false, "message" => "Penerimaan pembelian tidak ditemukan"));
            return;
        }
        
        // Validate status transition
        $valid_transition = false;
        if ($penerimaan->status == 'draft' && $status == 'diterima') {
            $valid_transition = true;
            $data = array(
                'status' => $status,
                'penerima' => $this->input->post('penerima'),
                'tanggal_diterima' => date('Y-m-d H:i:s'),
                'updated_by' => $this->session->userdata('id_user'),
                'updated_at' => date('Y-m-d H:i:s')
            );
        } else if ($penerimaan->status == 'diterima' && $status == 'selesai') {
            $valid_transition = true;
            $data = array(
                'status' => $status,
                'updated_by' => $this->session->userdata('id_user'),
                'updated_at' => date('Y-m-d H:i:s')
            );
        }
        
        if (!$valid_transition) {
            echo json_encode(array("status" => false, "message" => "Perubahan status tidak valid"));
            return;
        }
        
        $update = $this->Mod_penerimaan_pembelian->update($id, $data);
        
        if ($update) {
            // If status is 'diterima', create barang masuk
            if ($status == 'diterima') {
                $barang_masuk_id = $this->Mod_penerimaan_pembelian->create_barang_masuk($id);
                
                // Update pembelian status
                $this->Mod_penerimaan_pembelian->update_pembelian_status($penerimaan->id_pembelian);
                
                if ($barang_masuk_id) {
                    echo json_encode(array(
                        "status" => true, 
                        "message" => "Status penerimaan pembelian berhasil diupdate dan barang masuk otomatis dibuat",
                        "barang_masuk_id" => $barang_masuk_id
                    ));
                } else {
                    echo json_encode(array(
                        "status" => true, 
                        "message" => "Status penerimaan pembelian berhasil diupdate tetapi gagal membuat barang masuk"
                    ));
                }
            } else {
                echo json_encode(array("status" => true, "message" => "Status penerimaan pembelian berhasil diupdate"));
            }
        } else {
            echo json_encode(array("status" => false, "message" => "Gagal mengupdate status penerimaan pembelian"));
        }
    }

    public function print($id)
    {
        $data['penerimaan'] = $this->Mod_penerimaan_pembelian->get_by_id($id);
        $data['detail_items'] = $this->Mod_penerimaan_pembelian->get_detail_by_penerimaan_id($id);
        $this->load->view('penerimaan/print_penerimaan', $data);
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        if ($this->input->post('nomor_penerimaan') == '') {
            $data['inputerror'][] = 'nomor_penerimaan';
            $data['error_string'][] = 'Nomor penerimaan harus diisi';
            $data['status'] = FALSE;
        }

        if ($this->input->post('tanggal_penerimaan') == '') {
            $data['inputerror'][] = 'tanggal_penerimaan';
            $data['error_string'][] = 'Tanggal penerimaan harus diisi';
            $data['status'] = FALSE;
        }

        if ($this->input->post('id_pembelian') == '') {
            $data['inputerror'][] = 'id_pembelian';
            $data['error_string'][] = 'Pembelian harus dipilih';
            $data['status'] = FALSE;
        }

        if ($this->input->post('id_supplier') == '') {
            $data['inputerror'][] = 'id_supplier';
            $data['error_string'][] = 'Supplier harus dipilih';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }
}