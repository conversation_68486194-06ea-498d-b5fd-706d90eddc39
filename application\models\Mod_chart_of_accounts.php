<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Mod_chart_of_accounts extends CI_Model
{
    var $table = 'chart_of_accounts';
    var $column_order = array('kode_akun', 'nama_akun', 'tipe_akun', 'kategori_akun', 'saldo_normal', 'is_active');
    var $column_search = array('kode_akun', 'nama_akun', 'tipe_akun', 'kategori_akun');
    var $order = array('kode_akun' => 'asc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->from($this->table);
        
        $i = 0;
        foreach ($this->column_search as $item) {
            if($_POST['search']['value']) {
                if($i===0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                
                if(count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }
        
        if(isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if(isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    public function get_by_id($id)
    {
        $this->db->from($this->table);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_by_kode($kode_akun)
    {
        $this->db->from($this->table);
        $this->db->where('kode_akun', $kode_akun);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_parent_accounts()
    {
        $this->db->from($this->table);
        //$this->db->where('level <', 3); // Only show accounts with level < 3 as parent options
        $this->db->where('is_active', 'Y');
        $this->db->order_by('kode_akun', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_active_accounts()
    {
        $this->db->from($this->table);
        $this->db->where('is_active', 'Y');
        $this->db->order_by('kode_akun', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_accounts_by_type($tipe_akun)
    {
        $this->db->from($this->table);
        $this->db->where('tipe_akun', $tipe_akun);
        $this->db->where('is_active', 'Y');
        $this->db->order_by('kode_akun', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function insert($data)
    {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
        return $this->db->affected_rows();
    }

    public function delete($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table);
        return $this->db->affected_rows();
    }

    public function has_children($id)
    {
        $this->db->from($this->table);
        $this->db->where('parent_id', $id);
        return $this->db->count_all_results() > 0;
    }

    public function is_used_in_transactions($id)
    {
        // Check if account is used in jurnal_detail
        $this->db->from('jurnal_detail');
        $this->db->where('id_akun', $id);
        $count = $this->db->count_all_results();
        
        return $count > 0;
    }

    public function get_account_balance($id_akun, $tanggal_akhir = null)
    {
        if (!$tanggal_akhir) {
            $tanggal_akhir = date('Y-m-d');
        }
        
        $this->db->select('
            COALESCE(SUM(jd.debit), 0) as total_debit,
            COALESCE(SUM(jd.kredit), 0) as total_kredit,
            coa.saldo_normal
        ');
        $this->db->from('jurnal_detail jd');
        $this->db->join('jurnal_umum ju', 'jd.id_jurnal = ju.id');
        $this->db->join('chart_of_accounts coa', 'jd.id_akun = coa.id');
        $this->db->where('jd.id_akun', $id_akun);
        $this->db->where('ju.tanggal <=', $tanggal_akhir);
        $this->db->where('ju.status', 'POSTED');
        $this->db->group_by('jd.id_akun, coa.saldo_normal');
        
        $query = $this->db->get();
        $result = $query->row();
        
        if (!$result) {
            return 0;
        }
        
        // Calculate balance based on normal balance
        if ($result->saldo_normal == 'DEBIT') {
            return $result->total_debit - $result->total_kredit;
        } else {
            return $result->total_kredit - $result->total_debit;
        }
    }

    public function get_trial_balance($tanggal_akhir = null)
    {
        if (!$tanggal_akhir) {
            $tanggal_akhir = date('Y-m-d');
        }
        
        $this->db->select('
            coa.id,
            coa.kode_akun,
            coa.nama_akun,
            coa.tipe_akun,
            coa.saldo_normal,
            COALESCE(SUM(jd.debit), 0) as total_debit,
            COALESCE(SUM(jd.kredit), 0) as total_kredit
        ');
        $this->db->from('chart_of_accounts coa');
        $this->db->join('jurnal_detail jd', 'coa.id = jd.id_akun', 'left');
        $this->db->join('jurnal_umum ju', 'jd.id_jurnal = ju.id', 'left');
        $this->db->where('coa.is_active', 'Y');
        $this->db->where('coa.level', 3); // Only detail accounts
        if ($tanggal_akhir) {
            $this->db->group_start();
            $this->db->where('ju.tanggal <=', $tanggal_akhir);
            $this->db->where('ju.status', 'POSTED');
            $this->db->or_where('ju.id IS NULL');
            $this->db->group_end();
        }
        $this->db->group_by('coa.id, coa.kode_akun, coa.nama_akun, coa.tipe_akun, coa.saldo_normal');
        $this->db->order_by('coa.kode_akun', 'asc');
        
        $query = $this->db->get();
        return $query->result();
    }
}
