<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON><PERSON>_pajak extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_jenis_pajak');
        $this->load->model('Mod_dashboard');
    }

    public function index()
    {
        $link = 'jenis_pajak';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $this->template->load('layoutbackend', 'jenis_pajak/jenis_pajak', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $this->set_resource_limits();
        $list = $this->Mod_jenis_pajak->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->kode_pajak ?? '-';
            $sub_array[] = $row->nama_pajak;
            $sub_array[] = $row->tarif_persen . '%';
            $sub_array[] = date('d/m/Y', strtotime($row->tanggal_mulai));
            $sub_array[] = $row->tanggal_selesai ? date('d/m/Y', strtotime($row->tanggal_selesai)) : 'Tidak Terbatas';
            $sub_array[] = ($row->aktif == 1) ? '<span class="badge badge-success">Aktif</span>' : '<span class="badge badge-danger">Tidak Aktif</span>';

            $sub_array[] = "<a class=\"btn btn-xs btn-outline-primary edit\" href=\"javascript:void(0)\" title=\"Edit\" onclick=\"edit('$row->id')\"><i class=\"fas fa-edit\"></i></a>
                             <a class=\"btn btn-xs btn-outline-danger delete\" href=\"javascript:void(0)\" title=\"Delete\" onclick=\"hapus('$row->id')\"><i class=\"fas fa-trash\"></i></a>";
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_jenis_pajak->count_all(),
            "recordsFiltered" => $this->Mod_jenis_pajak->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert()
    {
        $this->_validate();

        // Generate kode otomatis jika tidak diisi
        $kode = $this->input->post('kode_pajak');
        if (empty($kode)) {
            $kode = $this->Mod_jenis_pajak->generate_kode();
        }

        $save = array(
            'kode_pajak' => $kode,
            'nama_pajak' => $this->input->post('nama_pajak'),
            'tarif_persen' => $this->input->post('tarif_persen'),
            'tanggal_mulai' => $this->input->post('tanggal_mulai'),
            'tanggal_selesai' => $this->input->post('tanggal_selesai') ?: null,
            'keterangan' => $this->input->post('keterangan'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );
        $this->Mod_jenis_pajak->insert('jenis_pajak', $save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $save = array(
            'nama_pajak' => $this->input->post('nama_pajak'),
            'tarif_persen' => $this->input->post('tarif_persen'),
            'tanggal_mulai' => $this->input->post('tanggal_mulai'),
            'tanggal_selesai' => $this->input->post('tanggal_selesai') ?: null,
            'keterangan' => $this->input->post('keterangan'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );
        $this->Mod_jenis_pajak->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_jenis_pajak->get($id);
        echo json_encode($data);
    }

    public function form_input()
    {
        $this->load->view('jenis_pajak/form_input');
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_jenis_pajak->delete($id);
        echo json_encode(array("status" => TRUE));
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        // Validasi kode pajak
        $kode = $this->input->post('kode_pajak');
        $id = $this->input->post('id');
        if (!empty($kode)) {
            if ($this->Mod_jenis_pajak->check_kode_exists($kode, $id)) {
                $data['inputerror'][] = 'kode_pajak';
                $data['error_string'][] = 'Kode Pajak Sudah Ada';
                $data['status'] = FALSE;
            }
        }

        // Validasi nama pajak (wajib)
        if ($this->input->post('nama_pajak') == '') {
            $data['inputerror'][] = 'nama_pajak';
            $data['error_string'][] = 'Nama Pajak Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        // Validasi tarif persen (wajib)
        if ($this->input->post('tarif_persen') == '' || $this->input->post('tarif_persen') < 0) {
            $data['inputerror'][] = 'tarif_persen';
            $data['error_string'][] = 'Tarif Persen Tidak Boleh Kosong atau Negatif';
            $data['status'] = FALSE;
        }

        // Validasi tanggal mulai (wajib)
        if ($this->input->post('tanggal_mulai') == '') {
            $data['inputerror'][] = 'tanggal_mulai';
            $data['error_string'][] = 'Tanggal Mulai Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        // Validasi tanggal selesai tidak boleh lebih kecil dari tanggal mulai
        $tanggal_mulai = $this->input->post('tanggal_mulai');
        $tanggal_selesai = $this->input->post('tanggal_selesai');
        if (!empty($tanggal_selesai) && !empty($tanggal_mulai)) {
            if (strtotime($tanggal_selesai) < strtotime($tanggal_mulai)) {
                $data['inputerror'][] = 'tanggal_selesai';
                $data['error_string'][] = 'Tanggal Selesai Tidak Boleh Lebih Kecil dari Tanggal Mulai';
                $data['status'] = FALSE;
            }
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    // Method untuk generate kode otomatis via AJAX
    public function generate_kode()
    {
        $kode = $this->Mod_jenis_pajak->generate_kode();
        echo json_encode(array('kode' => $kode));
    }

    // Method untuk toggle status aktif
    public function toggle_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        $result = $this->Mod_jenis_pajak->update_status($id, $status);
        echo json_encode(array("status" => $result));
    }
}
