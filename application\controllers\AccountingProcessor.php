<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AccountingProcessor extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_accounting_integration');
        $this->load->model('Mod_jurnal_umum');
        
       /*  // This controller should only be accessible via CLI or internal calls
        if (!$this->input->is_cli_request() && !$this->session->userdata('status')) {
            show_404();
        } */
    }

    public function process_pending_transactions()
    {
        // Get pending transactions
        $this->db->where('status', 'PENDING');
        $this->db->order_by('created_at', 'asc');
        $this->db->limit(50); // Process max 50 at a time
        $pending_transactions = $this->db->get('accounting_integration_log')->result();
        
        $processed = 0;
        $errors = 0;
        
        foreach ($pending_transactions as $transaction) {
            try {
                // Check if journal already exists for this transaction
                if ($this->is_journal_exists($transaction->tipe_transaksi, $transaction->ref_id)) {
                    // Mark as processed since journal already exists
                    $this->db->where('id', $transaction->id);
                    $this->db->update('accounting_integration_log', array(
                        'status' => 'PROCESSED',
                        'error_message' => 'Journal already exists - marked as processed',
                        'processed_at' => date('Y-m-d H:i:s')
                    ));
                    $processed++;
                    continue;
                }
                
                $jurnal_id = null;
                
                switch ($transaction->tipe_transaksi) {
                    case 'PENJUALAN':
                        $jurnal_id = $this->Mod_accounting_integration->create_sales_journal($transaction->ref_id);
                        break;
                        
                    case 'PEMBELIAN':
                        $jurnal_id = $this->Mod_accounting_integration->create_purchase_journal($transaction->ref_id);
                        break;
                        
                    case 'PEMBAYARAN_PENJUALAN':
                        $payment_data = $this->get_payment_data($transaction->ref_id, 'penjualan');
                        if ($payment_data) {
                            $jurnal_id = $this->Mod_accounting_integration->create_payment_journal($payment_data, 'penjualan');
                        }
                        break;
                        
                    case 'PEMBAYARAN_PEMBELIAN':
                        $payment_data = $this->get_payment_data($transaction->ref_id, 'pembelian');
                        if ($payment_data) {
                            $jurnal_id = $this->Mod_accounting_integration->create_payment_journal($payment_data, 'pembelian');
                        }
                        break;
                }
                
                if ($jurnal_id) {
                    // Update status to processed
                    $this->db->where('id', $transaction->id);
                    $this->db->update('accounting_integration_log', array(
                        'status' => 'PROCESSED',
                        'jurnal_id' => $jurnal_id,
                        'processed_at' => date('Y-m-d H:i:s')
                    ));
                    $processed++;
                } else {
                    // Mark as error
                    $this->db->where('id', $transaction->id);
                    $this->db->update('accounting_integration_log', array(
                        'status' => 'ERROR',
                        'error_message' => 'Failed to create journal entry',
                        'processed_at' => date('Y-m-d H:i:s')
                    ));
                    $errors++;
                }
                
            } catch (Exception $e) {
                // Mark as error with exception message
                $this->db->where('id', $transaction->id);
                $this->db->update('accounting_integration_log', array(
                    'status' => 'ERROR',
                    'error_message' => $e->getMessage(),
                    'processed_at' => date('Y-m-d H:i:s')
                ));
                $errors++;
            }
        }
        
        if ($this->input->is_cli_request()) {
            echo "Processed: $processed, Errors: $errors\n";
        } else {
            echo json_encode(array(
                'status' => TRUE,
                'processed' => $processed,
                'errors' => $errors
            ));
        }
    }

    private function get_payment_data($payment_id, $type)
    {
        if ($type == 'penjualan') {
            $this->db->select('fpp.*, fp.nomor_faktur');
            $this->db->from('faktur_penjualan_pembayaran fpp');
            $this->db->join('faktur_penjualan fp', 'fpp.id_faktur_penjualan = fp.id');
            $this->db->where('fpp.id', $payment_id);
            $payment = $this->db->get()->row();
            
            if ($payment) {
                return array(
                    'jumlah_pembayaran' => $payment->jumlah_pembayaran,
                    'metode_pembayaran' => $payment->metode_pembayaran,
                    'tanggal_pembayaran' => $payment->tanggal_pembayaran,
                    'keterangan' => 'Pembayaran faktur ' . $payment->nomor_faktur,
                    'ref_transaksi' => $payment->nomor_faktur
                );
            }
        } else {
            $this->db->select('pp.*, p.nomor_pembelian');
            $this->db->from('pembelian_pembayaran pp');
            $this->db->join('pembelian p', 'pp.id_pembelian = p.id');
            $this->db->where('pp.id', $payment_id);
            $payment = $this->db->get()->row();
            
            if ($payment) {
                return array(
                    'jumlah_pembayaran' => $payment->jumlah_bayar,
                    'metode_pembayaran' => $payment->metode_pembayaran,
                    'tanggal_pembayaran' => $payment->tanggal_pembayaran,
                    'keterangan' => 'Pembayaran pembelian ' . $payment->nomor_pembelian,
                    'ref_transaksi' => $payment->nomor_pembelian
                );
            }
        }
        
        return null;
    }
    
    private function is_journal_exists($tipe_transaksi, $ref_id)
    {
        // Check if journal already exists based on transaction type and reference
        $ref_transaksi = '';
        
        switch ($tipe_transaksi) {
            case 'PENJUALAN':
                // Get nomor_faktur from faktur_penjualan
                $this->db->select('nomor_faktur');
                $this->db->where('id', $ref_id);
                $faktur = $this->db->get('faktur_penjualan')->row();
                if ($faktur) {
                    $ref_transaksi = $faktur->nomor_faktur;
                }
                break;
                
            case 'PEMBELIAN':
                // Get nomor_pembelian from pembelian
                $this->db->select('nomor_pembelian');
                $this->db->where('id', $ref_id);
                $pembelian = $this->db->get('pembelian')->row();
                if ($pembelian) {
                    $ref_transaksi = $pembelian->nomor_pembelian;
                }
                break;
                
            case 'PEMBAYARAN_PENJUALAN':
                // Get nomor_faktur from payment reference
                $this->db->select('fp.nomor_faktur');
                $this->db->from('faktur_penjualan_pembayaran fpp');
                $this->db->join('faktur_penjualan fp', 'fpp.id_faktur_penjualan = fp.id');
                $this->db->where('fpp.id', $ref_id);
                $payment = $this->db->get()->row();
                if ($payment) {
                    $ref_transaksi = $payment->nomor_faktur;
                }
                break;
                
            case 'PEMBAYARAN_PEMBELIAN':
                // Get nomor_pembelian from payment reference
                $this->db->select('p.nomor_pembelian');
                $this->db->from('pembelian_pembayaran pp');
                $this->db->join('pembelian p', 'pp.id_pembelian = p.id');
                $this->db->where('pp.id', $ref_id);
                $payment = $this->db->get()->row();
                if ($payment) {
                    $ref_transaksi = $payment->nomor_pembelian;
                }
                break;
        }
        
        if (empty($ref_transaksi)) {
            return false;
        }
        
        // Check if journal exists with this ref_transaksi and tipe_transaksi
        $this->db->where('ref_transaksi', $ref_transaksi);
        $this->db->where('tipe_transaksi', 'AUTO_' . $tipe_transaksi);
        $existing = $this->db->get('jurnal_umum')->row();
        
        return ($existing !== null);
    }

    public function manual_process($type, $ref_id)
    {
        // Manual processing for specific transaction
        try {
            // Check if journal already exists
            $tipe_transaksi = strtoupper($type);
            if ($this->is_journal_exists($tipe_transaksi, $ref_id)) {
                echo json_encode(array(
                    'status' => FALSE,
                    'message' => 'Jurnal sudah ada untuk transaksi ini'
                ));
                return;
            }
            
            $jurnal_id = null;
            
            switch ($type) {
                case 'penjualan':
                    $jurnal_id = $this->Mod_accounting_integration->create_sales_journal($ref_id);
                    break;
                    
                case 'pembelian':
                    $jurnal_id = $this->Mod_accounting_integration->create_purchase_journal($ref_id);
                    break;
            }
            
            if ($jurnal_id) {
                echo json_encode(array(
                    'status' => TRUE,
                    'message' => 'Jurnal berhasil dibuat',
                    'jurnal_id' => $jurnal_id
                ));
            } else {
                echo json_encode(array(
                    'status' => FALSE,
                    'message' => 'Gagal membuat jurnal'
                ));
            }
            
        } catch (Exception $e) {
            echo json_encode(array(
                'status' => FALSE,
                'message' => $e->getMessage()
            ));
        }
    }

    public function get_integration_status()
    {
        // Get integration statistics
        $this->db->select('status, COUNT(*) as count');
        $this->db->group_by('status');
        $stats = $this->db->get('accounting_integration_log')->result();
        
        $result = array(
            'PENDING' => 0,
            'PROCESSED' => 0,
            'ERROR' => 0
        );
        
        foreach ($stats as $stat) {
            $result[$stat->status] = $stat->count;
        }
        
        echo json_encode(array(
            'status' => TRUE,
            'data' => $result
        ));
    }

    public function retry_failed()
    {
        // Retry failed transactions
        $this->db->where('status', 'ERROR');
        $this->db->update('accounting_integration_log', array(
            'status' => 'PENDING',
            'error_message' => null,
            'processed_at' => null
        ));
        
        $affected = $this->db->affected_rows();
        
        echo json_encode(array(
            'status' => TRUE,
            'message' => "$affected transaksi akan diproses ulang"
        ));
    }
}
