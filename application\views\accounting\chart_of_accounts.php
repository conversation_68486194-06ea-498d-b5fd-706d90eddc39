<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fas fa-list-alt text-blue"></i> Chart of Accounts</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add_account()" title="Add Account"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_accounts" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Kode Akun</th>
                                    <th>Nama <PERSON>kun</th>
                                    <th>Tipe</th>
                                    <th>Kate<PERSON>i</th>
                                    <th>Saldo Normal</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>

<!-- Modal Form -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-chart-line"></i> Form Chart of Accounts</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Info Panel -->
                <div class="alert alert-info" role="alert">
                    <h6 class="alert-heading"><i class="fas fa-info-circle"></i> Panduan Chart of Accounts</h6>
                    <small>
                        <strong>Kode Akun:</strong> Format standar (1xxx Aset, 2xxx Kewajiban) | 
                        <strong>Parent Account:</strong> Untuk hierarki | 
                        <strong>Saldo Normal:</strong> Debit (Aset/Beban), Kredit (Kewajiban/Modal/Pendapatan)
                    </small>
                </div>

                <form action="#" id="form">
                    <input type="hidden" value="" name="id"/>
                    
                    <!-- Basic Information -->
                    <div class="card card-outline card-primary mb-3">
                        <div class="card-header">
                            <h6 class="card-title"><i class="fas fa-edit"></i> Basic Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><i class="fas fa-hashtag"></i> Kode Akun <span class="text-danger">*</span></label>
                                        <input name="kode_akun" placeholder="Contoh: 1101" class="form-control" type="text" required>
                                        <small class="form-text text-muted">Kode unik untuk identifikasi akun</small>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><i class="fas fa-tag"></i> Nama Akun <span class="text-danger">*</span></label>
                                        <input name="nama_akun" placeholder="Contoh: Kas" class="form-control" type="text" required>
                                        <small class="form-text text-muted">Nama deskriptif untuk akun</small>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Classification -->
                    <div class="card card-outline card-success mb-3">
                        <div class="card-header">
                            <h6 class="card-title"><i class="fas fa-sitemap"></i> Classification</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><i class="fas fa-layer-group"></i> Tipe Akun <span class="text-danger">*</span></label>
                                        <select name="tipe_akun" class="form-control" required>
                                            <option value="">-- Pilih Tipe Akun --</option>
                                            <option value="ASET">ASET (Harta)</option>
                                            <option value="KEWAJIBAN">KEWAJIBAN (Utang)</option>
                                            <option value="MODAL">MODAL (Ekuitas)</option>
                                            <option value="PENDAPATAN">PENDAPATAN (Revenue)</option>
                                            <option value="BEBAN">BEBAN (Expense)</option>
                                        </select>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><i class="fas fa-tags"></i> Kategori <span class="text-danger">*</span></label>
                                        <select name="kategori_akun" class="form-control" required>
                                            <option value="">-- Pilih Kategori --</option>
                                            <optgroup label="ASET">
                                                <option value="ASET_LANCAR">ASET LANCAR</option>
                                                <option value="ASET_TETAP">ASET TETAP</option>
                                            </optgroup>
                                            <optgroup label="KEWAJIBAN">
                                                <option value="KEWAJIBAN_LANCAR">KEWAJIBAN LANCAR</option>
                                                <option value="KEWAJIBAN_JANGKA_PANJANG">KEWAJIBAN JANGKA PANJANG</option>
                                            </optgroup>
                                            <optgroup label="MODAL">
                                                <option value="MODAL_PEMILIK">MODAL PEMILIK</option>
                                            </optgroup>
                                            <optgroup label="PENDAPATAN">
                                                <option value="PENDAPATAN_USAHA">PENDAPATAN USAHA</option>
                                                <option value="PENDAPATAN_LAIN">PENDAPATAN LAIN</option>
                                            </optgroup>
                                            <optgroup label="BEBAN">
                                                <option value="BEBAN_OPERASIONAL">BEBAN OPERASIONAL</option>
                                                <option value="BEBAN_NON_OPERASIONAL">BEBAN NON OPERASIONAL</option>
                                            </optgroup>
                                        </select>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Settings -->
                    <div class="card card-outline card-warning mb-3">
                        <div class="card-header">
                            <h6 class="card-title"><i class="fas fa-cogs"></i> Advanced Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><i class="fas fa-code-branch"></i> Parent Account</label>
                                        <select name="parent_id" class="form-control">
                                            <option value="">-- Tidak Ada Parent --</option>
                                        </select>
                                        <small class="form-text text-muted">Untuk sub-akun (opsional)</small>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><i class="fas fa-balance-scale"></i> Saldo Normal <span class="text-danger">*</span></label>
                                        <select name="saldo_normal" class="form-control" required>
                                            <option value="">-- Pilih Saldo Normal --</option>
                                            <option value="DEBIT">DEBIT (Kiri)</option>
                                            <option value="KREDIT">KREDIT (Kanan)</option>
                                        </select>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><i class="fas fa-toggle-on"></i> Status <span class="text-danger">*</span></label>
                                        <select name="is_active" class="form-control" required>
                                            <option value="">-- Pilih Status --</option>
                                            <option value="Y">Aktif</option>
                                            <option value="N">Tidak Aktif</option>
                                        </select>
                                        <span class="help-block text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <small class="text-muted mr-auto">
                    <i class="fas fa-info-circle"></i> 
                    Akun akan otomatis tersedia setelah disimpan
                </small>
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">
                    <i class="fas fa-save"></i> Simpan
                </button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script type="text/javascript">
    var save_method;
    var table;

    $(document).ready(function() {
        table = $('#tbl_accounts').DataTable({
            "processing": true,
            "serverSide": true,
            "order": [],
            "ajax": {
                "url": "<?php echo site_url('ChartOfAccounts/ajax_list')?>",
                "type": "POST"
            },
            "columnDefs": [
                {
                    "targets": [-1],
                    "orderable": false,
                },
            ],
        });

        $('#form').on('submit', function(e) {
            e.preventDefault();
        });
    });

    function add_account() {
        save_method = 'add';
        $('#form')[0].reset();
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        $('#modal_form').modal('show');
        $('.modal-title').text('Add Account');
        load_parent_accounts();
    }

    function edit_account(id) {
        save_method = 'update';
        $('#form')[0].reset();
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();

        $.ajax({
            url: "<?php echo site_url('ChartOfAccounts/edit_form')?>/" + id,
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                $('[name="id"]').val(data.id);
                $('[name="kode_akun"]').val(data.kode_akun);
                $('[name="nama_akun"]').val(data.nama_akun);
                $('[name="tipe_akun"]').val(data.tipe_akun);
                $('[name="kategori_akun"]').val(data.kategori_akun);
                $('[name="saldo_normal"]').val(data.saldo_normal);
                $('[name="is_active"]').val(data.is_active);
                $('#modal_form').modal('show');
                $('.modal-title').text('Edit Account');
                load_parent_accounts(data.parent_id);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
    }

    function load_parent_accounts(selected_id) {
        var select = $('[name="parent_id"]');
        select.empty();
        select.append('<option value="">-- Tidak Ada Parent --</option>');
        $.ajax({
            url: "<?php echo site_url('ChartOfAccounts/get_parent_accounts')?>",
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                if (data && data.length > 0) {
                    $.each(data, function(i, acc) {
                        var indent = '';
                        for (var j = 1; j < acc.level; j++) indent += '&nbsp;&nbsp;&nbsp;';
                        var selected = (selected_id && acc.id == selected_id) ? 'selected' : '';
                        select.append('<option value="'+acc.id+'" data-tipe="'+acc.tipe_akun+'" data-kategori="'+acc.kategori_akun+'" '+selected+'>'+indent+acc.kode_akun+' - '+acc.nama_akun+'</option>');
                    });
                }
            },
            error: function() {
                select.append('<option value="">[Gagal load parent akun]</option>');
            }
        });
    }

    // Helper untuk sinkronisasi hidden input
    function syncHidden(fieldName) {
        var field = $('[name="'+fieldName+'"]');
        var hidden = $('.hidden-'+fieldName);
        if (field.prop('disabled')) {
            if (hidden.length === 0) {
                field.after('<input type="hidden" name="'+fieldName+'" value="'+field.val()+'" class="hidden-'+fieldName+'">');
            } else {
                hidden.val(field.val());
            }
        } else {
            hidden.remove();
        }
    }

    $(document).on('change', '[name="parent_id"]', function() {
        var selected = $(this).find('option:selected');
        var tipe = selected.data('tipe');
        var kategori = selected.data('kategori');
        var tipeField = $('[name="tipe_akun"]');
        var kategoriField = $('[name="kategori_akun"]');
        // Hindari trigger berulang dan infinite loop
        tipeField.off('change.sync').on('change.sync', function() { syncHidden('tipe_akun'); });
        kategoriField.off('change.sync').on('change.sync', function() { syncHidden('kategori_akun'); });
        if (tipe) {
            tipeField.val(tipe);
            tipeField.prop('disabled', true);
        } else {
            tipeField.prop('disabled', false);
            tipeField.val('');
        }
        if (kategori) {
            kategoriField.val(kategori);
            kategoriField.prop('disabled', true);
        } else {
            kategoriField.prop('disabled', false);
            kategoriField.val('');
        }
        syncHidden('tipe_akun');
        syncHidden('kategori_akun');
    });

    $(document).on('change', '[name="tipe_akun"],[name="kategori_akun"]', function() {
        syncHidden($(this).attr('name'));
    });

    function save() {
        $('#btnSave').text('saving...');
        $('#btnSave').attr('disabled', true);
        var url;

        if (save_method == 'add') {
            url = "<?php echo site_url('ChartOfAccounts/insert')?>";
        } else {
            url = "<?php echo site_url('ChartOfAccounts/update')?>";
        }

        $.ajax({
            url: url,
            type: "POST",
            data: $('#form').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) {
                    $('#modal_form').modal('hide');
                    reload_table();
                    toastr.success('Data berhasil disimpan');
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error');
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]);
                    }
                }
                $('#btnSave').text('save');
                $('#btnSave').attr('disabled', false);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error adding / update data');
                $('#btnSave').text('save');
                $('#btnSave').attr('disabled', false);
            }
        });
    }

    function delete_account(id) {
        if (confirm('Are you sure delete this data?')) {
            $.ajax({
                url: "<?php echo site_url('ChartOfAccounts/delete')?>",
                type: "POST",
                dataType: "JSON",
                data: {id: id},
                success: function(data) {
                    if (data.status) {
                        reload_table();
                        toastr.success('Data berhasil dihapus');
                    } else {
                        toastr.error(data.message);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    alert('Error deleting data');
                }
            });
        }
    }

    function reload_table() {
        table.ajax.reload(null, false);
    }
</script>
