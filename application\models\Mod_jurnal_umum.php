<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Mod_jurnal_umum extends CI_Model
{
    var $table = 'jurnal_umum';
    var $column_order = array('nomor_jurnal', 'tanggal', 'keterangan', 'tipe_transaksi', 'total_debit', 'status');
    var $column_search = array('nomor_jurnal', 'keterangan', 'tipe_transaksi');
    var $order = array('tanggal' => 'desc', 'nomor_jurnal' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->from($this->table);
        
        $i = 0;
        foreach ($this->column_search as $item) {
            if($_POST['search']['value']) {
                if($i===0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                
                if(count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }
        
        if(isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if(isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    public function get_by_id($id)
    {
        $this->db->from($this->table);
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_detail_by_jurnal_id($id_jurnal)
    {
        $this->db->select('jd.*, coa.kode_akun, coa.nama_akun');
        $this->db->from('jurnal_detail jd');
        $this->db->join('chart_of_accounts coa', 'jd.id_akun = coa.id');
        $this->db->where('jd.id_jurnal', $id_jurnal);
        $this->db->order_by('jd.urutan', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Generate nomor jurnal otomatis dengan format JU-YYYYMM9999
     * @return string Nomor jurnal yang unik
     */
    public function generate_nomor()
    {
        $tahun = date('Y');
        $bulan = date('m');
        $prefix = 'JU-' . $tahun . $bulan;
        
        // Gunakan database lock untuk mencegah race condition
        $this->db->query('LOCK TABLES ' . $this->table . ' WRITE');
        
        try {
            // Get last number for current month
            $this->db->select('nomor_jurnal');
            $this->db->from($this->table);
            $this->db->like('nomor_jurnal', $prefix, 'after');
            $this->db->order_by('nomor_jurnal', 'desc');
            $this->db->limit(1);
            $query = $this->db->get();
            
            if ($query->num_rows() > 0) {
                $last_nomor = $query->row()->nomor_jurnal;
                $last_number = (int) substr($last_nomor, -4);
                $new_number = $last_number + 1;
            } else {
                $new_number = 1;
            }
            
            $nomor_jurnal = $prefix . str_pad($new_number, 4, '0', STR_PAD_LEFT);
            
            // Unlock tables
            $this->db->query('UNLOCK TABLES');
            
            return $nomor_jurnal;
            
        } catch (Exception $e) {
            $this->db->query('UNLOCK TABLES');
            log_message('error', 'Error generate nomor jurnal: ' . $e->getMessage());
            // Fallback dengan timestamp jika terjadi error
            return $prefix . str_pad(date('His'), 4, '0', STR_PAD_LEFT);
        }
    }

    /**
     * Insert jurnal dengan detail dan validasi error
     * @param array $jurnal_data Data header jurnal
     * @param array $detail_data Data detail jurnal
     * @return int|false ID jurnal yang dibuat atau false jika gagal
     */
    public function insert($jurnal_data, $detail_data = array())
    {
        $this->db->trans_start();
        
        try {
            // Insert jurnal header
            $this->db->insert($this->table, $jurnal_data);
            $id_jurnal = $this->db->insert_id();
            
            if (!$id_jurnal) {
                throw new Exception('Gagal insert jurnal header');
            }
            
            // Insert jurnal detail
            if (!empty($detail_data)) {
                foreach ($detail_data as $index => $detail) {
                    // Validasi data detail
                    if (!isset($detail['id_akun']) || !isset($detail['keterangan'])) {
                        throw new Exception('Data detail jurnal tidak lengkap pada index: ' . $index);
                    }
                    
                    $detail_insert = array(
                        'id_jurnal' => $id_jurnal,
                        'id_akun' => $detail['id_akun'],
                        'keterangan' => $detail['keterangan'],
                        'debit' => isset($detail['debit']) ? $detail['debit'] : 0,
                        'kredit' => isset($detail['kredit']) ? $detail['kredit'] : 0,
                        'urutan' => $index + 1
                    );
                    
                    $this->db->insert('jurnal_detail', $detail_insert);
                    
                    if ($this->db->affected_rows() == 0) {
                        throw new Exception('Gagal insert jurnal detail pada index: ' . $index);
                    }
                }
            }
            
            $this->db->trans_complete();
            
            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Transaksi database gagal');
            }
            
            return $id_jurnal;
            
        } catch (Exception $e) {
            $this->db->trans_rollback();
            log_message('error', 'Error insert jurnal: ' . $e->getMessage());
            return FALSE;
        }
    }

    public function update($id, $jurnal_data, $detail_data = array())
    {
        $this->db->trans_start();
        
        // Update jurnal header
        $this->db->where('id', $id);
        $this->db->update($this->table, $jurnal_data);
        
        // Update jurnal detail if provided
        if (!empty($detail_data)) {
            // Delete existing detail
            $this->db->where('id_jurnal', $id);
            $this->db->delete('jurnal_detail');
            
            // Insert new detail
            foreach ($detail_data as $index => $detail) {
                $detail_insert = array(
                    'id_jurnal' => $id,
                    'id_akun' => $detail['id_akun'],
                    'keterangan' => $detail['keterangan'],
                    'debit' => $detail['debit'],
                    'kredit' => $detail['kredit'],
                    'urutan' => $index + 1
                );
                $this->db->insert('jurnal_detail', $detail_insert);
            }
        }
        
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    public function delete($id)
    {
        $this->db->trans_start();
        
        // Delete jurnal detail first
        $this->db->where('id_jurnal', $id);
        $this->db->delete('jurnal_detail');
        
        // Delete jurnal header
        $this->db->where('id', $id);
        $this->db->delete($this->table);
        
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    /**
     * Update buku besar berdasarkan jurnal yang dibuat
     * @param int $id_jurnal ID jurnal yang akan diupdate ke buku besar
     * @return bool Status update
     */
    public function update_buku_besar($id_jurnal)
    {
        try {
            // Get jurnal data
            $jurnal = $this->get_by_id($id_jurnal);
            if (!$jurnal) {
                throw new Exception('Jurnal tidak ditemukan: ID=' . $id_jurnal);
            }
            
            $periode = date('Y-m', strtotime($jurnal->tanggal));
            
            // Get jurnal detail
            $detail = $this->get_detail_by_jurnal_id($id_jurnal);
            if (empty($detail)) {
                throw new Exception('Detail jurnal tidak ditemukan: ID=' . $id_jurnal);
            }
            
            $this->db->trans_start();
            
            foreach ($detail as $item) {
                // Check if buku besar record exists
                $this->db->where('id_akun', $item->id_akun);
                $this->db->where('periode', $periode);
                $existing = $this->db->get('buku_besar')->row();
                
                if ($existing) {
                    // Update existing record
                    $this->db->set('total_debit', 'total_debit + ' . $item->debit, FALSE);
                    $this->db->set('total_kredit', 'total_kredit + ' . $item->kredit, FALSE);
                    $this->db->where('id', $existing->id);
                    $this->db->update('buku_besar');
                    
                    if ($this->db->affected_rows() == 0) {
                        throw new Exception('Gagal update buku besar untuk akun: ' . $item->id_akun);
                    }
                } else {
                    // Insert new record
                    $buku_besar_data = array(
                        'id_akun' => $item->id_akun,
                        'periode' => $periode,
                        'saldo_awal' => 0,
                        'total_debit' => $item->debit,
                        'total_kredit' => $item->kredit,
                        'saldo_akhir' => 0
                    );
                    $this->db->insert('buku_besar', $buku_besar_data);
                    
                    if ($this->db->affected_rows() == 0) {
                        throw new Exception('Gagal insert buku besar untuk akun: ' . $item->id_akun);
                    }
                }
                
                // Update saldo akhir
                $this->_update_saldo_akhir($item->id_akun, $periode);
            }
            
            $this->db->trans_complete();
            
            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Transaksi update buku besar gagal');
            }
            
            return TRUE;
            
        } catch (Exception $e) {
            $this->db->trans_rollback();
            log_message('error', 'Error update buku besar: ' . $e->getMessage() . ', Jurnal ID: ' . $id_jurnal);
            return FALSE;
        }
    }

    private function _update_saldo_akhir($id_akun, $periode)
    {
        // Get account normal balance
        $this->db->select('saldo_normal');
        $this->db->where('id', $id_akun);
        $account = $this->db->get('chart_of_accounts')->row();
        
        // Get buku besar record
        $this->db->where('id_akun', $id_akun);
        $this->db->where('periode', $periode);
        $buku_besar = $this->db->get('buku_besar')->row();
        
        if ($buku_besar && $account) {
            // Calculate saldo akhir based on normal balance
            if ($account->saldo_normal == 'DEBIT') {
                $saldo_akhir = $buku_besar->saldo_awal + $buku_besar->total_debit - $buku_besar->total_kredit;
            } else {
                $saldo_akhir = $buku_besar->saldo_awal + $buku_besar->total_kredit - $buku_besar->total_debit;
            }
            
            // Update saldo akhir
            $this->db->where('id', $buku_besar->id);
            $this->db->update('buku_besar', array('saldo_akhir' => $saldo_akhir));
        }
    }

    /**
     * Membuat jurnal otomatis dengan validasi duplikasi
     * @param string $tipe_transaksi Tipe transaksi jurnal
     * @param string $ref_transaksi Referensi transaksi
     * @param string $tanggal Tanggal jurnal
     * @param string $keterangan Keterangan jurnal
     * @param array $detail_data Detail jurnal (debit/kredit)
     * @return int|false ID jurnal yang dibuat atau false jika gagal
     */
    public function create_auto_journal($tipe_transaksi, $ref_transaksi, $tanggal, $keterangan, $detail_data)
    {
        // Validasi keseimbangan debit dan kredit
        $total_debit = array_sum(array_column($detail_data, 'debit'));
        $total_kredit = array_sum(array_column($detail_data, 'kredit'));
        
        if ($total_debit != $total_kredit) {
            log_message('error', 'Jurnal tidak seimbang: Debit=' . $total_debit . ', Kredit=' . $total_kredit . ', Ref=' . $ref_transaksi);
            return FALSE;
        }
        
        // Cek duplikasi berdasarkan ref_transaksi dan tipe_transaksi
        $this->db->where('ref_transaksi', $ref_transaksi);
        $this->db->where('tipe_transaksi', $tipe_transaksi);
        $existing_journal = $this->db->get($this->table)->row();
        
        if ($existing_journal) {
            log_message('info', 'Jurnal sudah ada untuk ref_transaksi: ' . $ref_transaksi . ', tipe: ' . $tipe_transaksi . ', ID: ' . $existing_journal->id);
            return $existing_journal->id; // Return ID jurnal yang sudah ada
        }
        
        // Buat data jurnal baru
        $jurnal_data = array(
            'nomor_jurnal' => $this->generate_nomor(),
            'tanggal' => $tanggal,
            'keterangan' => $keterangan,
            'ref_transaksi' => $ref_transaksi,
            'tipe_transaksi' => $tipe_transaksi,
            'total_debit' => $total_debit,
            'total_kredit' => $total_kredit,
            'status' => 'POSTED',
            'created_by' => 'SYSTEM',
            'posted_at' => date('Y-m-d H:i:s'),
            'posted_by' => 'SYSTEM'
        );
        
        // Insert jurnal dengan detail
        $id_jurnal = $this->insert($jurnal_data, $detail_data);
        
        if ($id_jurnal) {
            // Update buku besar
            $this->update_buku_besar($id_jurnal);
            log_message('info', 'Jurnal berhasil dibuat: ID=' . $id_jurnal . ', Ref=' . $ref_transaksi . ', Tipe=' . $tipe_transaksi);
        } else {
            log_message('error', 'Gagal membuat jurnal: Ref=' . $ref_transaksi . ', Tipe=' . $tipe_transaksi);
        }
        
        return $id_jurnal;
    }
}
