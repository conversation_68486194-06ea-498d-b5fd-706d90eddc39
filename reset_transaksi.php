<?php
die("Admin only!");
// ##################################################################################
// # PERINGATAN KERAS!                                                              #
// # =================                                                              #
// # Skrip ini akan MENGHAPUS SEMUA DATA TRANSAKSI secara permanen.                 #
// # PASTIKAN ANDA SUDAH MEMBUAT BACKUP DATABASE SEBELUM MENJALANKAN SKRIP INI.     #
// # Setelah selesai digunakan, SEGERA HAPUS file ini dari server Anda.            #
// ##################################################################################

// Konfigurasi
// Lokasi file konfigurasi database CodeIgniter
$config_path = 'application/config/database.php';

// Daftar tabel transaksi yang akan dikosongkan (TRUNCATE)
// Sesuaikan daftar ini jika ada tabel lain atau ada yang tidak ingin dihapus.
$transaction_tables = [
    'pembelian',
    'pembelian_detail',
    'pembelian_pembayaran',
    'pembelian_tracking',
    'penerimaan_pembelian',
    'penerimaan_pembelian_detail',
    'retur_pembelian',
    'retur_pembelian_detail',
    'pesanan',
    'pesanan_detail',
    'pengiriman',
    'pengiriman_detail',
    'faktur_penjualan',
    'faktur_penjualan_detail',
    'faktur_penjualan_pembayaran',
    'retur_penjualan',
    'retur_penjualan_detail',
    'barang_keluar',
    'barang_keluar_detail',
    'barang_masuk',
    'barang_masuk_detail',
    'stok_opname',
    'stok_opname_detail',
    'stok_barang',
    'stok_movement',
    'penyesuaian_stok',
    'transfer_stok',
    'transfer_stok_detail',
    'jurnal_umum',
    'jurnal_detail', // perbaikan dari 'jurnal_umum_detail'
    'buku_besar',
    'accounting_integration_log'
];

// --- Mulai Skrip ---
header('Content-Type: text/plain');

// 1. Baca konfigurasi database CodeIgniter
if (!file_exists($config_path)) {
    die("ERROR: File konfigurasi database tidak ditemukan di: " . $config_path);
}
// Variabel ini akan tersedia setelah include file config
// Tambahkan BASEPATH agar database.php bisa di-include tanpa error
if (!defined('BASEPATH')) define('BASEPATH', true);
// Tambahkan ENVIRONMENT agar database.php tidak warning
if (!defined('ENVIRONMENT')) define('ENVIRONMENT', 'development');
$db = [];
include($config_path);

$active_group = 'default';
$hostname = $db[$active_group]['hostname'];
$username = $db[$active_group]['username'];
$password = $db[$active_group]['password'];
$database = $db[$active_group]['database'];

// 2. Buat koneksi ke database
$conn = new mysqli($hostname, $username, $password, $database);
if ($conn->connect_error) {
    die("Koneksi Gagal: " . $conn->connect_error);
}
echo "Koneksi database berhasil.\n\n";

// 3. Proses pengosongan tabel
echo "Memulai proses pengosongan tabel...\n";
echo "-------------------------------------\n";

// Matikan foreign key checks
$conn->query('SET FOREIGN_KEY_CHECKS=0');
echo "[INFO] Foreign key checks dinonaktifkan.\n";

foreach ($transaction_tables as $table) {
    $sql = "TRUNCATE TABLE `" . $table . "`";
    if ($conn->query($sql) === TRUE) {
        echo "[OK] Tabel '$table' berhasil dikosongkan.\n";
    } else {
        // Cek apakah tabel ada atau tidak
        $check_table_sql = "SHOW TABLES LIKE '$table'";
        $result = $conn->query($check_table_sql);
        if ($result && $result->num_rows == 0) {
            echo "[WARN] Tabel '$table' tidak ditemukan, dilewati.\n";
        } else {
            echo "[ERROR] Gagal mengosongkan tabel '$table': (" . $conn->errno . ") " . $conn->error . "\n";
        }
    }
}

// Aktifkan kembali foreign key checks
$conn->query('SET FOREIGN_KEY_CHECKS=1');
echo "[INFO] Foreign key checks diaktifkan kembali.\n";

echo "\n-------------------------------------\n";
echo "Proses selesai.\n\n";
echo "PENTING: Jangan lupa untuk MENGHAPUS file 'reset_transaksi.php' ini dari server Anda!\n";

// Tutup koneksi
$conn->close();
?>