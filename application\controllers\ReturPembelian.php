<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Retur Pembelian
 * Mengatur retur pembelian dan detailnya
 * Terintegrasi dengan modul pembelian dan penerimaan pembelian
 */
class ReturPembelian extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_retur_pembelian', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'retur_pembelian/retur_pembelian', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $this->set_resource_limits();
        $list = $this->Mod_retur_pembelian->get_datatables();
        $data = array();
        foreach ($list as $retur) {
            $row = array();
            $row[] = $retur->nomor_retur;
            $row[] = date('d/m/Y', strtotime($retur->tanggal_retur));
            $row[] = $retur->nomor_pembelian;
            $row[] = $retur->nomor_penerimaan ? $retur->nomor_penerimaan : '-';
            $row[] = $retur->nama_supplier . ' (' . $retur->kode_supplier . ')';
            
            // Status badge
            switch ($retur->status) {
                case 'draft':
                    $status_badge = '<span class="badge badge-warning">Draft</span>';
                    break;
                case 'diproses':
                    $status_badge = '<span class="badge badge-primary">Diproses</span>';
                    break;
                case 'selesai':
                    $status_badge = '<span class="badge badge-success">Selesai</span>';
                    break;
                case 'dibatalkan':
                    $status_badge = '<span class="badge badge-danger">Dibatalkan</span>';
                    break;
                default:
                    $status_badge = '<span class="badge badge-secondary">' . ucfirst($retur->status) . '</span>';
            }
            $row[] = $status_badge;
            
            $row[] = number_format($retur->total_item ?? 0, 0) . ' item';
            $row[] = number_format($retur->total_qty ?? 0, 0);
            $row[] = 'Rp ' . number_format($retur->total_nilai ?? 0, 0);
            
            // Action buttons
            $actions = '';
            if ($retur->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $retur->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $retur->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success process" href="javascript:void(0)" title="Proses" onclick="updateStatus(' . $retur->id . ', \'diproses\')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $retur->id . ')"><i class="fas fa-trash"></i></a>';
            } else if ($retur->status == 'diproses') {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $retur->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success complete" href="javascript:void(0)" title="Selesai" onclick="updateStatus(' . $retur->id . ', \'selesai\')"><i class="fas fa-check-double"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printRetur(' . $retur->id . ')"><i class="fas fa-print"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $retur->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printRetur(' . $retur->id . ')"><i class="fas fa-print"></i></a>';
            }

            $row[] = $actions;
            
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_retur_pembelian->count_all(),
            "recordsFiltered" => $this->Mod_retur_pembelian->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    // Method untuk form input modal
    public function form_input()
    {
        // Load dropdown data untuk form
        $data['supplier_list'] = $this->Mod_retur_pembelian->get_supplier_aktif();
        $data['pembelian_list'] = $this->Mod_retur_pembelian->get_pembelian_aktif();
        $data['penerimaan_list'] = $this->Mod_retur_pembelian->get_penerimaan_aktif();
        $data['nomor_retur'] = $this->Mod_retur_pembelian->generate_nomor_retur();
        $this->load->view('retur_pembelian/form_input', $data);
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_retur_pembelian->generate_nomor_retur();
        echo json_encode(array('nomor' => $nomor));
    }

    public function get_pembelian_info()
    {
        $id = $this->input->post('id');
        $pembelian = $this->Mod_retur_pembelian->get_pembelian_by_id($id);

        if ($pembelian) {
            // Dapatkan daftar penerimaan untuk pembelian ini
            $penerimaan_list = $this->Mod_retur_pembelian->get_penerimaan_by_pembelian($id);
            
            echo json_encode(array(
                'status' => true, 
                'data' => $pembelian,
                'penerimaan_list' => $penerimaan_list
            ));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Data pembelian tidak ditemukan'));
        }
    }

    public function get_penerimaan_info()
    {
        $id = $this->input->post('id');
        $penerimaan = $this->Mod_retur_pembelian->get_penerimaan_by_id($id);

        if ($penerimaan) {
            echo json_encode(array('status' => true, 'data' => $penerimaan));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Data penerimaan tidak ditemukan'));
        }
    }

    public function get_penerimaan_items()
    {
        $id = $this->input->post('id');
        $items = $this->Mod_retur_pembelian->get_penerimaan_detail($id);

        if ($items) {
            echo json_encode(array('status' => true, 'data' => $items));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Item penerimaan tidak ditemukan'));
        }
    }

    public function form_detail_item()
    {
        $id_retur = $this->input->post('id_retur');
        $id_penerimaan = $this->input->post('id_penerimaan');
        
        // Get retur data if id_retur is provided
        if ($id_retur) {
            $retur = $this->Mod_retur_pembelian->get_by_id($id_retur);
            if ($retur) {
                $id_penerimaan = $retur->id_penerimaan;
            }
        }
        
        // Periksa detail penerimaan terlebih dahulu
        $penerimaan_detail = [];
        if ($id_penerimaan) {
            $penerimaan_detail = $this->Mod_retur_pembelian->get_penerimaan_detail($id_penerimaan);
            log_message('info', 'Penerimaan ID: ' . $id_penerimaan . ' memiliki ' . count($penerimaan_detail) . ' item');
            
            // Jika tidak ada item dalam penerimaan
            if (empty($penerimaan_detail)) {
                $data['received_items'] = [];
                $data['error_message'] = 'Penerimaan ini tidak memiliki item. Silakan pilih penerimaan lain.';
                $data['gudang_list'] = $this->Mod_retur_pembelian->get_gudang_aktif();
                $data['id_retur'] = $id_retur;
                $data['id_penerimaan'] = $id_penerimaan;
                $this->load->view('retur_pembelian/form_detail_item', $data);
                return;
            }
        }
        
        // Get received items for returning
        $data['received_items'] = [];
        $existing_retur_details = [];
        
        // Jika sedang mengedit retur, ambil detail retur yang sudah ada
        if ($id_retur) {
            $existing_retur_details = $this->Mod_retur_pembelian->get_detail_by_retur_id($id_retur);
            // Buat array untuk mapping berdasarkan id_penerimaan_detail dan id_barang
            $existing_map = [];
            foreach ($existing_retur_details as $detail) {
                $key = $detail->id_penerimaan_detail . '_' . $detail->id_barang;
                $existing_map[$key] = $detail;
            }
        }
        
        if ($id_penerimaan) {
            try {
                $received_items = $this->Mod_retur_pembelian->get_penerimaan_detail($id_penerimaan);
                
                // Periksa apakah item sudah pernah diretur sebelumnya
                foreach ($received_items as $item) {
                    // Kecualikan qty dari retur yang sedang dibuat/diedit
                    $qty_already_returned = $this->Mod_retur_pembelian->get_qty_already_returned($item->id, $item->id_barang, $id_retur);
                    $item->qty_already_returned = $qty_already_returned; // Simpan untuk ditampilkan di view
                    
                    // Jika sedang mengedit, cek apakah item ini ada di detail retur yang sudah ada
                    $item->qty_retur_draft = 0;
                    $item->kondisi_barang_draft = 'baik';
                    $item->keterangan_item_draft = '';
                    $item->id_gudang_draft = $item->id_gudang;
                    
                    if ($id_retur && isset($existing_map)) {
                        $key = $item->id . '_' . $item->id_barang;
                        if (isset($existing_map[$key])) {
                            $existing_detail = $existing_map[$key];
                            $item->qty_retur_draft = $existing_detail->qty_retur;
                            $item->kondisi_barang_draft = $existing_detail->kondisi_barang;
                            $item->keterangan_item_draft = $existing_detail->keterangan;
                            $item->id_gudang_draft = $existing_detail->id_gudang;
                        }
                    }
                    
                    // Hitung qty_available: qty yang benar-benar tersedia untuk diretur
                    // Jangan tambahkan qty_retur_draft karena itu akan membuat total bisa melebihi qty_diterima
                    $qty_available = $item->qty_diterima - $qty_already_returned;
                    $item->qty_available = $qty_available;
                    
                    // Tampilkan item yang masih bisa diretur atau yang sudah ada di draft
                    if ($item->qty_available > 0 || ($id_retur && $item->qty_retur_draft > 0)) {
                        $data['received_items'][] = $item;
                    }
                }
                
                // Jika tidak ada item yang tersedia untuk retur
                if (empty($data['received_items'])) {
                    $data['error_message'] = 'Semua item dalam penerimaan ini sudah diretur sepenuhnya.';
                }
            } catch (Exception $e) {
                log_message('error', 'Error in form_detail_item: ' . $e->getMessage());
                $data['error_message'] = 'Terjadi kesalahan saat memuat data item.';
            }
        }
        
        // Load gudang dropdown
        try {
            $data['gudang_list'] = $this->Mod_retur_pembelian->get_gudang_aktif();
        } catch (Exception $e) {
            log_message('error', 'Error loading gudang list: ' . $e->getMessage());
            $data['gudang_list'] = [];
        }
        
        $data['id_retur'] = $id_retur;
        $data['id_penerimaan'] = $id_penerimaan;
        
        $this->load->view('retur_pembelian/form_detail_item', $data);
    }

    public function edit($id)
    {
        $data = $this->Mod_retur_pembelian->get_by_id($id);
        echo json_encode($data);
    }

    public function detail_modal($id)
    {
        $data['retur'] = $this->Mod_retur_pembelian->get_by_id($id);
        $data['detail_items'] = $this->Mod_retur_pembelian->get_detail_by_retur_id($id);
        
        // Jika ada referensi barang keluar, ambil datanya
        if (!empty($data['retur']->ref_barang_keluar)) {
            $this->load->model('Mod_barang_keluar');
            $data['barang_keluar'] = $this->Mod_barang_keluar->get($data['retur']->ref_barang_keluar);
        }
        
        $this->load->view('retur_pembelian/detail_modal', $data);
    }
    
    // Fungsi untuk melihat detail barang keluar dari retur
    public function view_barang_keluar($id_retur)
    {
        $retur = $this->Mod_retur_pembelian->get_by_id($id_retur);
        
        if (!$retur || empty($retur->ref_barang_keluar)) {
            echo json_encode(array("status" => false, "message" => "Data barang keluar tidak ditemukan"));
            return;
        }
        
        $this->load->model('Mod_barang_keluar');
        $barang_keluar = $this->Mod_barang_keluar->get($retur->ref_barang_keluar);
        
        if (!$barang_keluar) {
            echo json_encode(array("status" => false, "message" => "Data barang keluar tidak ditemukan"));
            return;
        }
        
        $detail_items = $this->Mod_barang_keluar->get_detail($retur->ref_barang_keluar);
        
        $data = array(
            "status" => true,
            "barang_keluar" => $barang_keluar,
            "detail_items" => $detail_items
        );
        
        echo json_encode($data);
    }

    public function insert()
    {
        $this->_validate();
        
        $data = array(
            'nomor_retur' => $this->input->post('nomor_retur'),
            'tanggal_retur' => $this->input->post('tanggal_retur'),
            'id_pembelian' => $this->input->post('id_pembelian'),
            'id_penerimaan' => $this->input->post('id_penerimaan'),
            'id_supplier' => $this->input->post('id_supplier'),
            'alasan_retur' => $this->input->post('alasan_retur'),
            'status' => 'draft',
            'keterangan' => $this->input->post('keterangan'),
            'created_by' => $this->session->userdata('id_user'),
            'created_at' => date('Y-m-d H:i:s')
        );
        
        $insert = $this->Mod_retur_pembelian->insert('retur_pembelian', $data);
        
        if ($insert) {
            // Process detail items if any
            $id_penerimaan_detail = $this->input->post('id_penerimaan_detail');
            $id_barang = $this->input->post('id_barang');
            $id_gudang = $this->input->post('id_gudang');
            $qty_diterima = $this->input->post('qty_diterima');
            $qty_retur = $this->input->post('qty_retur');
            $kondisi_barang = $this->input->post('kondisi_barang');
            $harga_satuan = $this->input->post('harga_satuan');
            $keterangan_item = $this->input->post('keterangan_item');
            
            if ($id_penerimaan_detail && is_array($id_penerimaan_detail)) {
                for ($i = 0; $i < count($id_penerimaan_detail); $i++) {
                    if (isset($qty_retur[$i]) && $qty_retur[$i] > 0) {
                        // Validasi server-side: cek qty available
                        $penerimaan_detail = $this->Mod_retur_pembelian->get_penerimaan_detail_by_id($id_penerimaan_detail[$i]);
                        if ($penerimaan_detail) {
                            // Kecualikan qty dari retur yang sedang dibuat/diedit untuk menghindari double counting
                            $qty_already_returned = $this->Mod_retur_pembelian->get_qty_already_returned($id_penerimaan_detail[$i], $id_barang[$i], $insert);
                            $qty_available = $penerimaan_detail->qty_diterima - $qty_already_returned;
                            
                            // Validasi qty retur tidak melebihi qty available
                            if ($qty_retur[$i] > $qty_available) {
                                echo json_encode(array(
                                    "status" => false, 
                                    "message" => "Qty retur untuk barang {$penerimaan_detail->nama_barang} melebihi qty tersedia ({$qty_available})"
                                ));
                                return;
                            }
                        }
                        
                        $detail_data = array(
                            'id_retur' => $insert,
                            'id_penerimaan_detail' => $id_penerimaan_detail[$i],
                            'id_barang' => $id_barang[$i],
                            'id_gudang' => $id_gudang[$i],
                            'qty_retur' => $qty_retur[$i],
                            'kondisi_barang' => $kondisi_barang[$i],
                            'harga_satuan' => $harga_satuan[$i],
                            'keterangan' => $keterangan_item[$i] ?? null,
                            'created_at' => date('Y-m-d H:i:s')
                        );
                        
                        $detail_insert = $this->Mod_retur_pembelian->save_detail($detail_data);
                        
                        // Audit trail untuk tracking perubahan qty retur
                        if ($detail_insert) {
                            $audit_message = "Retur pembelian detail ditambahkan - Barang: {$penerimaan_detail->nama_barang}, Qty: {$qty_retur[$i]}, Kondisi: {$kondisi_barang[$i]}";
                            log_message('info', "[RETUR_PEMBELIAN] User: {$this->session->userdata('nama_user')} - {$audit_message}");
                        }
                    }
                }
            }
            
            // Update total retur
            $this->Mod_retur_pembelian->update_total_retur($insert);
            
            // Audit trail untuk retur pembelian header
            $audit_message = "Retur pembelian berhasil dibuat - ID: {$insert}, Nomor: {$nomor_retur}, Supplier: {$data['id_supplier']}, Penerimaan: {$data['id_penerimaan']}";
            log_message('info', "[RETUR_PEMBELIAN] User: {$this->session->userdata('nama_user')} - {$audit_message}");
            
            echo json_encode(array("status" => true, "message" => "Retur pembelian berhasil disimpan"));
        } else {
            echo json_encode(array("status" => false, "message" => "Gagal menyimpan retur pembelian"));
        }
    }

    public function update()
    {
        $this->_validate();
        
        $id = $this->input->post('id');
        
        $data = array(
            'nomor_retur' => $this->input->post('nomor_retur'),
            'tanggal_retur' => $this->input->post('tanggal_retur'),
            'id_pembelian' => $this->input->post('id_pembelian'),
            'id_penerimaan' => $this->input->post('id_penerimaan'),
            'id_supplier' => $this->input->post('id_supplier'),
            'alasan_retur' => $this->input->post('alasan_retur'),
            'keterangan' => $this->input->post('keterangan'),
            'updated_by' => $this->session->userdata('id_user'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $update = $this->Mod_retur_pembelian->update($id, $data);
        
        if ($update || $update === 0) {
            // Hapus detail lama
            $this->Mod_retur_pembelian->delete_detail_by_retur($id);
            
            // Process detail items if any
            $id_penerimaan_detail = $this->input->post('id_penerimaan_detail');
            $id_barang = $this->input->post('id_barang');
            $id_gudang = $this->input->post('id_gudang');
            $qty_diterima = $this->input->post('qty_diterima');
            $qty_retur = $this->input->post('qty_retur');
            $kondisi_barang = $this->input->post('kondisi_barang');
            $harga_satuan = $this->input->post('harga_satuan');
            $keterangan_item = $this->input->post('keterangan_item');
            
            if ($id_penerimaan_detail && is_array($id_penerimaan_detail)) {
                for ($i = 0; $i < count($id_penerimaan_detail); $i++) {
                    if (isset($qty_retur[$i]) && $qty_retur[$i] > 0) {
                        // Validasi server-side: cek qty available
                        $penerimaan_detail = $this->Mod_retur_pembelian->get_penerimaan_detail_by_id($id_penerimaan_detail[$i]);
                        if ($penerimaan_detail) {
                            // Kecualikan qty dari retur yang sedang dibuat/diedit untuk menghindari double counting
                            $qty_already_returned = $this->Mod_retur_pembelian->get_qty_already_returned($id_penerimaan_detail[$i], $id_barang[$i], $id);
                            $qty_available = $penerimaan_detail->qty_diterima - $qty_already_returned;
                            
                            // Validasi qty retur tidak melebihi qty available
                            if ($qty_retur[$i] > $qty_available) {
                                echo json_encode(array(
                                    "status" => false, 
                                    "message" => "Qty retur untuk barang {$penerimaan_detail->nama_barang} melebihi qty tersedia ({$qty_available})"
                                ));
                                return;
                            }
                        }
                        
                        $detail_data = array(
                            'id_retur' => $id,
                            'id_penerimaan_detail' => $id_penerimaan_detail[$i],
                            'id_barang' => $id_barang[$i],
                            'id_gudang' => $id_gudang[$i],
                            'qty_retur' => $qty_retur[$i],
                            'kondisi_barang' => $kondisi_barang[$i],
                            'harga_satuan' => $harga_satuan[$i],
                            'keterangan' => $keterangan_item[$i] ?? null,
                            'created_at' => date('Y-m-d H:i:s')
                        );
                        
                        $this->Mod_retur_pembelian->save_detail($detail_data);
                    }
                }
            }
            
            // Update total retur
            $this->Mod_retur_pembelian->update_total_retur($id);
            
            echo json_encode(array("status" => true, "message" => "Retur pembelian berhasil diupdate"));
        } else {
            echo json_encode(array("status" => false, "message" => "Gagal mengupdate retur pembelian"));
        }
    }

    public function delete()
    {
        $id = $this->input->post('id');
        
        // Cek status retur
        $retur = $this->Mod_retur_pembelian->get_by_id($id);
        if ($retur->status != 'draft') {
            echo json_encode(array("status" => false, "message" => "Retur pembelian dengan status " . $retur->status . " tidak dapat dihapus"));
            return;
        }
        
        // Hapus detail terlebih dahulu
        $this->Mod_retur_pembelian->delete_detail_by_retur($id);
        
        // Hapus header
        $delete = $this->Mod_retur_pembelian->delete($id, 'retur_pembelian');
        
        if ($delete) {
            echo json_encode(array("status" => "success", "message" => "Retur pembelian berhasil dihapus"));
        } else {
            echo json_encode(array("status" => "error", "message" => "Gagal menghapus retur pembelian"));
        }
    }

    public function update_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        
        // Validasi status
        $valid_statuses = ['draft', 'diproses', 'selesai', 'dibatalkan'];
        if (!in_array($status, $valid_statuses)) {
            echo json_encode(array("status" => false, "message" => "Status tidak valid"));
            return;
        }
        
        // Cek retur
        $retur = $this->Mod_retur_pembelian->get_by_id($id);
        if (!$retur) {
            echo json_encode(array("status" => false, "message" => "Retur pembelian tidak ditemukan"));
            return;
        }
        
        // Validasi perubahan status
        $valid_change = false;
        if ($retur->status == 'draft' && ($status == 'diproses' || $status == 'dibatalkan')) {
            $valid_change = true;
        } else if ($retur->status == 'diproses' && ($status == 'selesai' || $status == 'dibatalkan')) {
            $valid_change = true;
        }
        
        if (!$valid_change) {
            echo json_encode(array("status" => false, "message" => "Perubahan status dari " . $retur->status . " ke " . $status . " tidak diperbolehkan"));
            return;
        }
        
        // Validasi ketersediaan item untuk retur saat status diproses atau selesai
        if ($status == 'diproses' || $status == 'selesai') {
            $detail_items = $this->Mod_retur_pembelian->get_detail_by_retur_id($id);
            
            foreach ($detail_items as $detail) {
                // Dapatkan informasi penerimaan detail
                $penerimaan_detail = $this->Mod_retur_pembelian->get_penerimaan_detail_by_id($detail->id_penerimaan_detail);
                
                if ($penerimaan_detail) {
                    // Hitung qty yang sudah diretur (kecualikan retur ini)
                    $qty_already_returned = $this->Mod_retur_pembelian->get_qty_already_returned(
                        $detail->id_penerimaan_detail, 
                        $detail->id_barang, 
                        $id
                    );
                    
                    // Hitung qty yang tersedia
                    $qty_available = $penerimaan_detail->qty_diterima - $qty_already_returned;
                    
                    // Validasi apakah qty retur masih valid
                    if ($detail->qty_retur > $qty_available) {
                        echo json_encode(array(
                            "status" => false, 
                            "message" => "Item {$penerimaan_detail->nama_barang} tidak dapat diproses. Qty retur ({$detail->qty_retur}) melebihi qty tersedia ({$qty_available}). Mungkin ada retur lain yang sudah diproses untuk item ini."
                        ));
                        return;
                    }
                }
            }
        }
        
        // Update status
        $data = array(
            'status' => $status,
            'updated_by' => $this->session->userdata('id_user'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $update = $this->Mod_retur_pembelian->update($id, $data);
        
        if ($update || $update === 0) {
            // Jika status diproses, buat barang keluar otomatis
            if ($status == 'diproses') {
                $this->load->model('Mod_barang_keluar');
                
                // Buat barang keluar dari retur
                $result_bk = $this->Mod_retur_pembelian->create_barang_keluar_from_retur($id);
                
                if (!$result_bk['status']) {
                    // Jika gagal membuat barang keluar, tetap lanjutkan proses
                    // tapi catat di log
                    log_message('error', 'Gagal membuat barang keluar otomatis: ' . $result_bk['message']);
                } else {
                    log_message('info', 'Barang keluar otomatis berhasil dibuat dengan ID: ' . $result_bk['id_barang_keluar']);
                }
            }
            
            // Jika status selesai, lakukan proses pengurangan stok
            if ($status == 'selesai') {
                // Proses pengurangan stok
                $result_stok = $this->Mod_retur_pembelian->update_stok_barang($id);
                
                if (!$result_stok['status']) {
                    // Jika gagal update stok, tetap lanjutkan proses
                    // tapi catat di log
                    log_message('error', 'Gagal update stok: ' . $result_stok['message']);
                } else {
                    log_message('info', 'Stok berhasil diperbarui untuk retur ID: ' . $id);
                }
            }
            
            echo json_encode(array("status" => true, "message" => "Status retur pembelian berhasil diupdate menjadi " . ucfirst($status)));
        } else {
            echo json_encode(array("status" => false, "message" => "Gagal mengupdate status retur pembelian"));
        }
    }

    public function print($id)
    {
        $data['retur'] = $this->Mod_retur_pembelian->get_by_id($id);
        $data['detail_items'] = $this->Mod_retur_pembelian->get_detail_by_retur_id($id);
        $this->load->view('retur_pembelian/print_retur', $data);
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        if ($this->input->post('nomor_retur') == '') {
            $data['inputerror'][] = 'nomor_retur';
            $data['error_string'][] = 'Nomor retur harus diisi';
            $data['status'] = FALSE;
        }

        if ($this->input->post('tanggal_retur') == '') {
            $data['inputerror'][] = 'tanggal_retur';
            $data['error_string'][] = 'Tanggal retur harus diisi';
            $data['status'] = FALSE;
        }

        if ($this->input->post('id_pembelian') == '') {
            $data['inputerror'][] = 'id_pembelian';
            $data['error_string'][] = 'Pembelian harus dipilih';
            $data['status'] = FALSE;
        }

        if ($this->input->post('id_penerimaan') == '') {
            $data['inputerror'][] = 'id_penerimaan';
            $data['error_string'][] = 'Penerimaan harus dipilih';
            $data['status'] = FALSE;
        }

        if ($this->input->post('id_supplier') == '') {
            $data['inputerror'][] = 'id_supplier';
            $data['error_string'][] = 'Supplier harus dipilih';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    // Method untuk mendapatkan semua penerimaan aktif
    public function get_all_penerimaan()
    {
        $penerimaan_list = $this->Mod_retur_pembelian->get_penerimaan_aktif();
        
        if ($penerimaan_list) {
            echo json_encode(array(
                'status' => true, 
                'data' => $penerimaan_list
            ));
        } else {
            echo json_encode(array(
                'status' => false, 
                'message' => 'Data penerimaan tidak ditemukan',
                'data' => []
            ));
        }
    }
}