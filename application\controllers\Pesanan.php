<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller <PERSON><PERSON><PERSON>
 * Mengatur pesanan pelanggan dan detailnya
 * Redesigned untuk konsistensi dengan modul master dan warehouse
 */
class <PERSON><PERSON>an extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_pesanan', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'pesanan/pesanan', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $this->set_resource_limits();
        $list = $this->Mod_pesanan->get_datatables();
        $data = array();
        foreach ($list as $pesanan) {
            $row = array();
            $row[] = $pesanan->nomor_pesanan;
            $row[] = date('d/m/Y', strtotime($pesanan->tanggal_pesanan));
            $row[] = $pesanan->nama_pelanggan . ' (' . $pesanan->kode_pelanggan . ')';
            
            // Jenis pesanan badge
            if ($pesanan->jenis_pesanan == 'android') {
                $jenis_badge = '<span class="badge badge-info">Android</span>';
            } else {
                $jenis_badge = '<span class="badge badge-secondary">Manual</span>';
            }
            $row[] = $jenis_badge;
            
            // Status badge
            switch ($pesanan->status) {
                case 'draft':
                    $status_badge = '<span class="badge badge-warning">Draft</span>';
                    break;
                case 'diproses':
                    $status_badge = '<span class="badge badge-primary">Diproses</span>';
                    break;
                case 'dikirim':
                    $status_badge = '<span class="badge badge-info">Dikirim</span>';
                    break;
                case 'selesai':
                    $status_badge = '<span class="badge badge-success">Selesai</span>';
                    break;
                case 'dibatalkan':
                    $status_badge = '<span class="badge badge-danger">Dibatalkan</span>';
                    break;
                default:
                    $status_badge = '<span class="badge badge-secondary">' . ucfirst($pesanan->status) . '</span>';
            }
            $row[] = $status_badge;
            
            $row[] = number_format($pesanan->total_item ?? 0, 0) . ' item';
            $row[] = number_format($pesanan->total_qty ?? 0, 0); // Format qty sebagai bilangan bulat sesuai memory
            $row[] = 'Rp ' . number_format($pesanan->total_akhir ?? 0, 0, ',', '.');
            
            // Action buttons
            $actions = '';
            if ($pesanan->status == 'draft') {
                $actions .= '<a class="btn btn-xs btn-outline-info edit" href="javascript:void(0)" title="Edit" onclick="edit(' . $pesanan->id . ')"><i class="fas fa-edit"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pesanan->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-success process" href="javascript:void(0)" title="Proses" onclick="updateStatus(' . $pesanan->id . ', \'diproses\')"><i class="fas fa-check"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $pesanan->id . ')"><i class="fas fa-trash"></i></a>';
            } else {
                $actions .= '<a class="btn btn-xs btn-outline-primary detail" href="javascript:void(0)" title="Detail" onclick="detail(' . $pesanan->id . ')"><i class="fas fa-list"></i></a> ';
                $actions .= '<a class="btn btn-xs btn-outline-info print" href="javascript:void(0)" title="Print" onclick="printPesanan(' . $pesanan->id . ')"><i class="fas fa-print"></i></a>';
            }

            $row[] = $actions;
            
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_pesanan->count_all(),
            "recordsFiltered" => $this->Mod_pesanan->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    // Method untuk form input modal (konsisten dengan modul lain)
    public function form_input()
    {
        // Load dropdown data untuk form
        $data['pelanggan_list'] = $this->Mod_pesanan->get_pelanggan_aktif();
        $data['nomor_pesanan'] = $this->Mod_pesanan->generate_nomor_pesanan();
        $this->load->view('pesanan/form_input', $data);
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_pesanan->generate_nomor_pesanan();
        echo json_encode(array('nomor' => $nomor));
    }

    public function get_pelanggan_info()
    {
        $id = $this->input->post('id');
        $pelanggan = $this->Mod_pesanan->get_pelanggan_by_id($id);

        if ($pelanggan) {
            echo json_encode(array('status' => true, 'data' => $pelanggan));
        } else {
            echo json_encode(array('status' => false, 'message' => 'Data pelanggan tidak ditemukan'));
        }
    }

    public function form_detail_item()
    {
        $data['barang_list'] = $this->Mod_pesanan->get_barang_aktif();
        $this->load->view('pesanan/form_detail_item', $data);
    }

    public function edit($id)
    {
        $data = $this->Mod_pesanan->get_by_id($id);
        echo json_encode($data);
    }

    // Method detail dihapus karena sudah menggunakan modal detail_modal



    public function add_form()
    {
        $data['pelanggan_list'] = $this->Mod_pesanan->get_pelanggan_aktif();
        $data['nomor_pesanan'] = $this->Mod_pesanan->generate_nomor_pesanan();
        $this->load->view('pesanan/form_input', $data);
    }
    
    public function edit_form($id)
    {
        $data['pesanan'] = $this->Mod_pesanan->get_by_id($id);
        $data['pelanggan_list'] = $this->Mod_pesanan->get_pelanggan_aktif();
        $this->load->view('pesanan/form_input', $data);
    }
    
    public function detail_modal($id)
    {
        $data['pesanan'] = $this->Mod_pesanan->get_by_id($id);
        $data['pesanan_detail'] = $this->Mod_pesanan->get_detail_by_pesanan_id($id);
        $data['barang_list'] = $this->Mod_pesanan->get_barang_aktif();
        $this->load->view('pesanan/detail_modal', $data);
    }
    
    public function insert()
    {
        $this->_validate();

        // Generate nomor otomatis jika tidak diisi
        $nomor = $this->input->post('nomor_pesanan');
        if (empty($nomor)) {
            $nomor = $this->Mod_pesanan->generate_nomor_pesanan();
        }

        $save = array(
            'nomor_pesanan' => $nomor,
            'tanggal_pesanan' => $this->input->post('tanggal_pesanan'),
            'id_pelanggan' => $this->input->post('id_pelanggan'),
            'jenis_pesanan' => $this->input->post('jenis_pesanan'),
            'status' => $this->input->post('status') ? $this->input->post('status') : 'draft',
            'keterangan' => $this->input->post('keterangan'),
            'total_item' => 0,
            'total_qty' => 0,
            'subtotal' => 0,
            'ppn_persen' => 11.00,
            'ppn_nominal' => 0,
            'total_setelah_ppn' => 0,
            'total_akhir' => 0,
            'created_by' => $this->session->userdata('nama_user'),
        );

        $this->Mod_pesanan->insert('pesanan', $save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $save = array(
            'tanggal_pesanan' => $this->input->post('tanggal_pesanan'),
            'id_pelanggan' => $this->input->post('id_pelanggan'),
            'jenis_pesanan' => $this->input->post('jenis_pesanan'),
            'status' => $this->input->post('status'),
            'keterangan' => $this->input->post('keterangan'),
            'updated_by' => $this->session->userdata('nama_user'),
        );

        $this->Mod_pesanan->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }
    
    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;
        
        if($this->input->post('nomor_pesanan') == '')
        {
            $data['inputerror'][] = 'nomor_pesanan';
            $data['error_string'][] = 'Nomor pesanan tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('tanggal_pesanan') == '')
        {
            $data['inputerror'][] = 'tanggal_pesanan';
            $data['error_string'][] = 'Tanggal pesanan tidak boleh kosong';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('id_pelanggan') == '')
        {
            $data['inputerror'][] = 'id_pelanggan';
            $data['error_string'][] = 'Pelanggan harus dipilih';
            $data['status'] = FALSE;
        }
        
        if($this->input->post('jenis_pesanan') == '')
        {
            $data['inputerror'][] = 'jenis_pesanan';
            $data['error_string'][] = 'Jenis pesanan harus dipilih';
            $data['status'] = FALSE;
        }
        
        if($data['status'] === FALSE)
        {
            echo json_encode($data);
            exit();
        }
    }

    public function delete()
    {
        $id = $this->input->post('id');

        // Cek apakah pesanan masih draft
        $pesanan = $this->Mod_pesanan->get_by_id($id);
        if($pesanan && $pesanan->status != 'draft') {
            echo json_encode(array('status' => FALSE, 'message' => 'Pesanan yang sudah dikonfirmasi tidak dapat dihapus!'));
            return;
        }

        // Hapus detail pesanan terlebih dahulu
        $this->Mod_pesanan->delete_detail_by_pesanan($id);

        // Hapus pesanan
        $delete_result = $this->Mod_pesanan->delete($id, 'pesanan');
        
        if($delete_result) {
            echo json_encode(array(
                "status" => 'success', 
                "message" => 'Data pesanan berhasil dihapus.'
            ));
        } else {
            echo json_encode(array(
                "status" => 'error', 
                "message" => 'Gagal menghapus data pesanan.'
            ));
        }
    }

    // Detail pesanan functions
    public function add_detail()
    {
        $id_pesanan = $this->input->post('id_pesanan');
        $id_barang = $this->input->post('id_barang');
        $qty = $this->input->post('qty');
        $harga_satuan = $this->input->post('harga_satuan');
        $keterangan = $this->input->post('keterangan');
        
        // Ambil data barang untuk mendapatkan PPN
        $this->db->select('b.*, jp.tarif_persen as pajak_persen');
        $this->db->from('barang b');
        $this->db->join('jenis_pajak jp', 'b.jenis_pajak_id = jp.id', 'left');
        $this->db->where('b.id', $id_barang);
        $barang = $this->db->get()->row();
        
        $subtotal = $qty * $harga_satuan;
        $ppn_persen = $barang->pajak_persen ? $barang->pajak_persen : 11.00; // Default PPN 11%
        $ppn_nominal = round($subtotal * $ppn_persen / 100, 2);
        $total_setelah_ppn = $subtotal + $ppn_nominal;
        
        $data = array(
            'id_pesanan' => $id_pesanan,
            'id_barang' => $id_barang,
            'qty' => $qty,
            'harga_satuan' => $harga_satuan,
            'subtotal' => $subtotal,
            'ppn_persen' => $ppn_persen,
            'ppn_nominal' => $ppn_nominal,
            'total_setelah_ppn' => $total_setelah_ppn,
            'keterangan' => $keterangan
        );
        
        $insert = $this->Mod_pesanan->save_detail($data);
        if ($insert) {
            $this->Mod_pesanan->update_total_pesanan($id_pesanan);
            echo json_encode(array('status' => 'success', 'message' => 'Item berhasil ditambahkan!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Item gagal ditambahkan!'));
        }
    }

    public function update_detail()
    {
        $id = $this->input->post('id');
        $id_pesanan = $this->input->post('id_pesanan');
        $qty = $this->input->post('qty');
        $harga_satuan = $this->input->post('harga_satuan');
        $keterangan = $this->input->post('keterangan');
        
        // Ambil data detail untuk mendapatkan id_barang
        $detail_existing = $this->Mod_pesanan->get_detail_by_id($id);
        
        // Ambil data barang untuk mendapatkan PPN
        $this->db->select('b.*, jp.tarif_persen as pajak_persen');
        $this->db->from('barang b');
        $this->db->join('jenis_pajak jp', 'b.jenis_pajak_id = jp.id', 'left');
        $this->db->where('b.id', $detail_existing->id_barang);
        $barang = $this->db->get()->row();
        
        $subtotal = $qty * $harga_satuan;
        $ppn_persen = $barang->pajak_persen ? $barang->pajak_persen : 11.00; // Default PPN 11%
        $ppn_nominal = round($subtotal * $ppn_persen / 100, 2);
        $total_setelah_ppn = $subtotal + $ppn_nominal;
        
        $data = array(
            'qty' => $qty,
            'harga_satuan' => $harga_satuan,
            'subtotal' => $subtotal,
            'ppn_persen' => $ppn_persen,
            'ppn_nominal' => $ppn_nominal,
            'total_setelah_ppn' => $total_setelah_ppn,
            'keterangan' => $keterangan
        );
        
        $update = $this->Mod_pesanan->update_detail(array('id' => $id), $data);
        if ($update) {
            $this->Mod_pesanan->update_total_pesanan($id_pesanan);
            echo json_encode(array('status' => 'success', 'message' => 'Item berhasil diupdate!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Item gagal diupdate!'));
        }
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $id_pesanan = $this->input->post('id_pesanan');
        
        $this->Mod_pesanan->delete_detail($id);
        $this->Mod_pesanan->update_total_pesanan($id_pesanan);
        
        echo json_encode(array('status' => 'success', 'message' => 'Item berhasil dihapus!'));
    }

    public function get_detail_item()
    {
        $id = $this->input->post('id');
        
        $detail = $this->Mod_pesanan->get_detail_by_id($id);
        if ($detail) {
            echo json_encode(array('status' => 'success', 'data' => $detail));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Data item tidak ditemukan!'));
        }
    }

    public function update_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        
        $data = array(
            'status' => $status,
            'updated_by' => $this->session->userdata('nama_user'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $update = $this->Mod_pesanan->update($id, $data);
        if ($update) {
            echo json_encode(array('status' => 'success', 'message' => 'Status pesanan berhasil diupdate!'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Status pesanan gagal diupdate!'));
        }
    }

    public function print_pesanan($id)
    {
        $data['pesanan'] = $this->Mod_pesanan->get_by_id($id);
        if (!$data['pesanan']) {
            show_404();
        }

        $data['detail'] = $this->Mod_pesanan->get_detail_by_pesanan_id($id);
        $this->load->view('pesanan/print_pesanan', $data);
    }
}