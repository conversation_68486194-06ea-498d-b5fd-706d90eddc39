<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Create By : Aryo
 * Youtube : <PERSON>ryo Coding
 */
class Satuan extends MY_Controller
{

    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_satuan');
        $this->load->model('Mod_dashboard');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');
        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view;
        }
        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'satuan/satuan', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $this->set_resource_limits();
        $list = $this->Mod_satuan->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $satuan) {
            $no++;
            $row = array();
            $row[] = $satuan->kode_satuan ?? '-';
            $row[] = $satuan->nama_satuan;
            $row[] = $satuan->keterangan ?? '-';
            $row[] = ($satuan->aktif == 1) ? '<span class="badge badge-success">Aktif</span>' : '<span class="badge badge-danger">Tidak Aktif</span>';
            $row[] = "<a class=\"btn btn-xs btn-outline-primary edit\" href=\"javascript:void(0)\" title=\"Edit\" onclick=\"edit('$satuan->id')\"><i class=\"fas fa-edit\"></i></a>
                      <a class=\"btn btn-xs btn-outline-danger delete\" href=\"javascript:void(0)\" title=\"Delete\" onclick=\"hapus('$satuan->id')\"><i class=\"fas fa-trash\"></i></a>";
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_satuan->count_all(),
            "recordsFiltered" => $this->Mod_satuan->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert()
    {
        $this->_validate();
        $kode = strtoupper($this->input->post('kode_satuan'));
        $save = array(
            'kode_satuan' => $kode,
            'nama_satuan' => $this->input->post('nama_satuan'),
            'keterangan' => $this->input->post('keterangan'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );
        $this->Mod_satuan->insert('satuan', $save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $save = array(
            'nama_satuan' => $this->input->post('nama_satuan'),
            'keterangan' => $this->input->post('keterangan'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );

        $this->Mod_satuan->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_satuan->get($id);
        echo json_encode($data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_satuan->delete($id);
        echo json_encode(array("status" => TRUE));
    }

    public function form_input()
    {
        $this->load->view('satuan/form_input');
    }

    // Method untuk toggle status aktif
    public function toggle_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        $result = $this->Mod_satuan->update_status($id, $status);
        echo json_encode(array("status" => $result));
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;
        $kode = strtoupper($this->input->post('kode_satuan'));
        $id = $this->input->post('id');
        // Kode wajib diisi
        if (empty($kode)) {
            $data['inputerror'][] = 'kode_satuan';
            $data['error_string'][] = 'Kode Satuan wajib diisi';
            $data['status'] = FALSE;
        } else {
            if (!preg_match('/^[A-Z0-9]{3,10}$/', $kode)) {
                $data['inputerror'][] = 'kode_satuan';
                $data['error_string'][] = 'Kode hanya huruf/angka, 3-10 karakter';
                $data['status'] = FALSE;
            } else if ($this->Mod_satuan->check_kode_exists($kode, $id)) {
                $data['inputerror'][] = 'kode_satuan';
                $data['error_string'][] = 'Kode Satuan sudah ada';
                $data['status'] = FALSE;
            }
        }
        if ($this->input->post('nama_satuan') == '') {
            $data['inputerror'][] = 'nama_satuan';
            $data['error_string'][] = 'Nama Satuan Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }
        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }
}
