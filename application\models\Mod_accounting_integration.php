<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Mod_accounting_integration extends CI_Model
{
    function __construct()
    {
        parent::__construct();
        $this->load->database();
        $this->load->model('Mod_jurnal_umum');
    }

    public function get_default_account($setting_name)
    {
        $this->db->select('id_akun');
        $this->db->where('nama_setting', $setting_name);
        $result = $this->db->get('akun_default')->row();
        
        return $result ? $result->id_akun : null;
    }

    public function create_sales_journal($faktur_id)
    {
        // Get faktur data
        $this->db->select('fp.*, p.nama as nama_pelanggan');
        $this->db->from('faktur_penjualan fp');
        $this->db->join('pelanggan p', 'fp.id_pelanggan = p.id', 'left');
        $this->db->where('fp.id', $faktur_id);
        $faktur = $this->db->get()->row();
        
        if (!$faktur) return false;
        
        // Validasi total_faktur
        if (!isset($faktur->total_faktur) || $faktur->total_faktur <= 0) {
            log_message('error', 'Total faktur tidak valid atau 0: ID=' . $faktur_id . ', Total=' . ($faktur->total_faktur ?? 'NULL'));
            return false;
        }
        
        // Get default accounts
        $akun_piutang = $this->get_default_account('PIUTANG_DAGANG');
        $akun_kas = $this->get_default_account('KAS');
        $akun_bank = $this->get_default_account('BANK');
        $akun_penjualan = $this->get_default_account('PENJUALAN');
        
        if (!$akun_penjualan) return false;
        
        // Determine debit account based on payment method
        $akun_debit = null;
        switch($faktur->metode_pembayaran) {
            case 'tunai':
                $akun_debit = $akun_kas;
                break;
            case 'transfer':
                $akun_debit = $akun_bank;
                break;
            case 'kredit':
                $akun_debit = $akun_piutang;
                break;
            default:
                $akun_debit = $akun_kas;
        }
        
        if (!$akun_debit) return false;
        
        // Create journal entry
        $detail_data = array(
            array(
                'id_akun' => $akun_debit,
                'keterangan' => 'Penjualan kepada ' . ($faktur->nama_pelanggan ?: 'Customer'),
                'debit' => $faktur->total_faktur,
                'kredit' => 0
            ),
            array(
                'id_akun' => $akun_penjualan,
                'keterangan' => 'Penjualan ' . $faktur->nomor_faktur,
                'debit' => 0,
                'kredit' => $faktur->total_faktur
            )
        );
        
        $keterangan = 'Jurnal otomatis penjualan - ' . $faktur->nomor_faktur;
        
        return $this->Mod_jurnal_umum->create_auto_journal(
            'AUTO_PENJUALAN',
            $faktur->nomor_faktur,
            $faktur->tanggal_faktur,
            $keterangan,
            $detail_data
        );
    }

    public function create_purchase_journal($pembelian_id)
    {
        // Get pembelian data
        $this->db->select('p.*, s.nama as nama_supplier');
        $this->db->from('pembelian p');
        $this->db->join('supplier s', 'p.id_supplier = s.id', 'left');
        $this->db->where('p.id', $pembelian_id);
        $pembelian = $this->db->get()->row();
        
        if (!$pembelian) return false;
        
        // Validasi total_akhir
        if (!isset($pembelian->total_akhir) || $pembelian->total_akhir <= 0) {
            log_message('error', 'Total pembelian tidak valid atau 0: ID=' . $pembelian_id . ', Total=' . ($pembelian->total_akhir ?? 'NULL'));
            return false;
        }
        
        // Get default accounts
        $akun_hutang = $this->get_default_account('HUTANG_DAGANG');
        $akun_kas = $this->get_default_account('KAS');
        $akun_bank = $this->get_default_account('BANK');
        $akun_pembelian = $this->get_default_account('PEMBELIAN');
        
        if (!$akun_pembelian) return false;
        
        // Untuk semua pembelian, selalu kredit ke Hutang Dagang
        // Pembayaran akan diproses terpisah melalui jurnal pembayaran
        $akun_kredit = $akun_hutang;
        
        if (!$akun_kredit) return false;
        
        // Create journal entry
        $detail_data = array(
            array(
                'id_akun' => $akun_pembelian,
                'keterangan' => 'Pembelian dari ' . ($pembelian->nama_supplier ?: 'Supplier'),
                'debit' => $pembelian->total_akhir,
                'kredit' => 0
            ),
            array(
                'id_akun' => $akun_kredit,
                'keterangan' => 'Pembelian ' . $pembelian->nomor_pembelian,
                'debit' => 0,
                'kredit' => $pembelian->total_akhir
            )
        );
        
        $keterangan = 'Jurnal otomatis pembelian - ' . $pembelian->nomor_pembelian;
        
        return $this->Mod_jurnal_umum->create_auto_journal(
            'AUTO_PEMBELIAN',
            $pembelian->nomor_pembelian,
            $pembelian->tanggal_pembelian,
            $keterangan,
            $detail_data
        );
    }

    public function create_payment_journal($payment_data, $type = 'penjualan')
    {
        // Get default accounts
        $akun_kas = $this->get_default_account('KAS');
        $akun_bank = $this->get_default_account('BANK');
        
        if ($type == 'penjualan') {
            $akun_piutang = $this->get_default_account('PIUTANG_DAGANG');
            $akun_receivable = $akun_piutang;
        } else {
            $akun_hutang = $this->get_default_account('HUTANG_DAGANG');
            $akun_receivable = $akun_hutang;
        }
        
        if (!$akun_receivable) return false;
        
        // Determine cash account based on payment method
        $akun_cash = null;
        switch($payment_data['metode_pembayaran']) {
            case 'tunai':
                $akun_cash = $akun_kas;
                break;
            case 'transfer':
                $akun_cash = $akun_bank;
                break;
            default:
                $akun_cash = $akun_kas;
        }
        
        if (!$akun_cash) return false;
        
        // Create journal entry
        if ($type == 'penjualan') {
            // Payment received
            $detail_data = array(
                array(
                    'id_akun' => $akun_cash,
                    'keterangan' => 'Pembayaran diterima',
                    'debit' => $payment_data['jumlah_pembayaran'],
                    'kredit' => 0
                ),
                array(
                    'id_akun' => $akun_receivable,
                    'keterangan' => 'Pelunasan piutang',
                    'debit' => 0,
                    'kredit' => $payment_data['jumlah_pembayaran']
                )
            );
        } else {
            // Payment made
            $detail_data = array(
                array(
                    'id_akun' => $akun_receivable,
                    'keterangan' => 'Pelunasan hutang',
                    'debit' => $payment_data['jumlah_pembayaran'],
                    'kredit' => 0
                ),
                array(
                    'id_akun' => $akun_cash,
                    'keterangan' => 'Pembayaran dilakukan',
                    'debit' => 0,
                    'kredit' => $payment_data['jumlah_pembayaran']
                )
            );
        }
        
        $keterangan = 'Jurnal otomatis pembayaran - ' . $payment_data['keterangan'];
        
        return $this->Mod_jurnal_umum->create_auto_journal(
            'AUTO_PEMBAYARAN',
            $payment_data['ref_transaksi'],
            $payment_data['tanggal_pembayaran'],
            $keterangan,
            $detail_data
        );
    }

    public function create_inventory_adjustment_journal($adjustment_data)
    {
        // Get default accounts
        $akun_persediaan = $this->get_default_account('PERSEDIAAN');
        
        if (!$akun_persediaan) return false;
        
        // For inventory adjustments, we need a corresponding account
        // This could be "Selisih Persediaan" or similar account
        // For now, we'll use a generic expense account
        $this->db->select('id');
        $this->db->where('kode_akun', '5299'); // Assuming this is "Beban Lain-lain"
        $akun_selisih = $this->db->get('chart_of_accounts')->row();
        
        if (!$akun_selisih) {
            // Create the account if it doesn't exist
            $akun_selisih_data = array(
                'kode_akun' => '5299',
                'nama_akun' => 'Beban Selisih Persediaan',
                'tipe_akun' => 'BEBAN',
                'kategori_akun' => 'BEBAN_OPERASIONAL',
                'level' => 3,
                'saldo_normal' => 'DEBIT',
                'is_active' => 'Y',
                'created_by' => 'SYSTEM'
            );
            $this->db->insert('chart_of_accounts', $akun_selisih_data);
            $akun_selisih_id = $this->db->insert_id();
        } else {
            $akun_selisih_id = $akun_selisih->id;
        }
        
        // Create journal entry based on adjustment type
        if ($adjustment_data['qty_adjustment'] > 0) {
            // Increase inventory
            $detail_data = array(
                array(
                    'id_akun' => $akun_persediaan,
                    'keterangan' => 'Penyesuaian persediaan (+)',
                    'debit' => $adjustment_data['nilai_adjustment'],
                    'kredit' => 0
                ),
                array(
                    'id_akun' => $akun_selisih_id,
                    'keterangan' => 'Selisih persediaan',
                    'debit' => 0,
                    'kredit' => $adjustment_data['nilai_adjustment']
                )
            );
        } else {
            // Decrease inventory
            $detail_data = array(
                array(
                    'id_akun' => $akun_selisih_id,
                    'keterangan' => 'Selisih persediaan',
                    'debit' => abs($adjustment_data['nilai_adjustment']),
                    'kredit' => 0
                ),
                array(
                    'id_akun' => $akun_persediaan,
                    'keterangan' => 'Penyesuaian persediaan (-)',
                    'debit' => 0,
                    'kredit' => abs($adjustment_data['nilai_adjustment'])
                )
            );
        }
        
        $keterangan = 'Jurnal otomatis penyesuaian stok - ' . $adjustment_data['keterangan'];
        
        return $this->Mod_jurnal_umum->create_auto_journal(
            'AUTO_STOK',
            $adjustment_data['ref_transaksi'],
            $adjustment_data['tanggal'],
            $keterangan,
            $detail_data
        );
    }
}
