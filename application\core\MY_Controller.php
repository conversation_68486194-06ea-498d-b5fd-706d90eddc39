<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MY_Controller extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if (!$this->session->userdata('logged_in')) {
            redirect('login/logout');
        }

        // Load common models and libraries
        $this->load->model('Mod_dashboard');
        $this->load->library('template');
    }

    public function check_csrf_token()
    {
        $csrf_token_name = $this->security->get_csrf_token_name(); // DARI LIBRARY security
        $posted_token = $this->input->post($csrf_token_name);
        return $posted_token === $this->security->get_csrf_hash();
    }

    /**
     * Mengatur batas sumber daya untuk memori dan waktu eksekusi.
     * Fungsi ini meningkatkan batas memori menjadi 512M dan waktu eksekusi menjadi 3600 detik.
     * Digunakan untuk operasi yang membutuhkan sumber daya besar seperti pembuatan laporan besar 
     * atau pemrosesan data yang signifikan pada permintaan AJAX.
     */
    protected function set_resource_limits()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
    }
}
