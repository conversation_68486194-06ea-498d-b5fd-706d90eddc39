<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Penyesuaian Stok
 * Mengatur penyesuaian stok barang di gudang
 */
class Penyesuaian extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_penyesuaian_stok', 'Mod_dashboard', 'Mod_accounting_integration'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        
        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view ?? 'N';
        }

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'penyesuaian_stok/penyesuaian_stok', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    /**
     * Method create untuk menampilkan form penyesuaian stok baru
     * URL: /Penyesuaian/create/{id_barang}/{id_gudang}
     */
    public function create($id_barang = null, $id_gudang = null)
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek akses menu
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->add ?? 'N';
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->add ?? 'N';
        }

        if ($akses == "Y") {
            // Load data untuk form
            $data['barang_list'] = $this->Mod_penyesuaian_stok->get_barang_dropdown();
            $data['gudang_list'] = $this->Mod_penyesuaian_stok->get_gudang_dropdown();
            
            // Jika ada parameter id_barang dan id_gudang, load data terkait
            if ($id_barang && $id_gudang) {
                $data['selected_barang'] = $this->Mod_penyesuaian_stok->get_barang_detail($id_barang);
                $data['selected_gudang'] = $this->Mod_penyesuaian_stok->get_gudang_detail($id_gudang);
                $data['stok_terakhir'] = $this->Mod_penyesuaian_stok->get_stok_terakhir($id_barang, $id_gudang);
            }
            
            $this->template->load('layoutbackend', 'penyesuaian_stok/form_create', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $this->set_resource_limits();
        $list = $this->Mod_penyesuaian_stok->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $penyesuaian) {
            $no++;
            $row = array();
            $row[] = $penyesuaian->kode_penyesuaian;
            $row[] = date('d/m/Y', strtotime($penyesuaian->tanggal));
            $row[] = $penyesuaian->nama_barang . '<br><small class="text-muted">' . $penyesuaian->kode_barang . '</small>';
            $row[] = $penyesuaian->nama_gudang . '<br><small class="text-muted">' . $penyesuaian->kode_gudang . '</small>';
            $row[] = number_format($penyesuaian->qty_awal, 0);
            $row[] = number_format($penyesuaian->qty_baru, 0);

            // Format selisih dengan warna
            $selisih_class = $penyesuaian->qty_selisih >= 0 ? 'text-success' : 'text-danger';
            $selisih_icon = $penyesuaian->qty_selisih >= 0 ? '+' : '';
            $row[] = '<span class="' . $selisih_class . '">' . $selisih_icon . number_format($penyesuaian->qty_selisih, 0) . '</span>';
            
            // Jenis penyesuaian dengan badge
            $jenis_class = $penyesuaian->jenis_penyesuaian == 'PENAMBAHAN' ? 'badge-success' : 'badge-danger';
            $row[] = '<span class="badge ' . $jenis_class . '">' . $penyesuaian->jenis_penyesuaian . '</span>';
            
            // Status aktif
            if ($penyesuaian->aktif == "1") {
                $row[] = '<span class="badge badge-success">Aktif</span>';
            } else {
                $row[] = '<span class="badge badge-secondary">Tidak Aktif</span>';
            }

            // Action buttons (hapus tombol edit)
            $row[] = '<a class="btn btn-xs btn-outline-danger delete" href="javascript:void(0)" title="Delete" onclick="hapus(' . $penyesuaian->id . ')"><i class="fas fa-trash"></i></a>';
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_penyesuaian_stok->count_all(),
            "recordsFiltered" => $this->Mod_penyesuaian_stok->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert()
    {
        $this->_validate();
        // Generate kode otomatis jika tidak diisi
        $kode = $this->input->post('kode_penyesuaian');
        if (empty($kode)) {
            $kode = $this->Mod_penyesuaian_stok->generate_kode();
        }
        // Hitung selisih dan jenis penyesuaian
        $qty_awal = (float) $this->input->post('qty_awal');
        $qty_baru = (float) $this->input->post('qty_baru');
        $qty_selisih = $qty_baru - $qty_awal;
        $jenis_penyesuaian = $qty_selisih >= 0 ? 'PENAMBAHAN' : 'PENGURANGAN';
        $save = array(
            'kode_penyesuaian' => $kode,
            'tanggal' => $this->input->post('tanggal'),
            'id_barang' => $this->input->post('id_barang'),
            'id_gudang' => $this->input->post('id_gudang'),
            'qty_awal' => $qty_awal,
            'qty_baru' => $qty_baru,
            'qty_selisih' => $qty_selisih,
            'jenis_penyesuaian' => $jenis_penyesuaian,
            'alasan' => $this->input->post('alasan'),
            'keterangan' => $this->input->post('keterangan'),
            'user_id' => $this->session->userdata('id_user'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );
        $this->Mod_penyesuaian_stok->insert('penyesuaian_stok', $save);
        // Ambil harga satuan dari master barang
        $id_barang = $this->input->post('id_barang');
        $barang = $this->Mod_penyesuaian_stok->get_barang_detail($id_barang);
        $harga_satuan = isset($barang->harga_beli) ? (float)$barang->harga_beli : 0; // Ganti field jika nama berbeda
        // Integrasi jurnal otomatis ke akuntansi
        $adjustment_data = array(
            'ref_transaksi' => $kode,
            'tanggal' => $this->input->post('tanggal'),
            'keterangan' => $this->input->post('keterangan'),
            'qty_adjustment' => $qty_selisih,
            'nilai_adjustment' => abs($qty_selisih) * $harga_satuan,
        );
        $this->Mod_accounting_integration->create_inventory_adjustment_journal($adjustment_data);
        echo json_encode(array("status" => TRUE));
    }

    public function form_input()
    {
        // Load dropdown data untuk form
        $data['barang_list'] = $this->Mod_penyesuaian_stok->get_barang_dropdown();
        $data['gudang_list'] = $this->Mod_penyesuaian_stok->get_gudang_dropdown();
        $this->load->view('penyesuaian_stok/form_input', $data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        // Ambil data penyesuaian sebelum dihapus
        $penyesuaian = $this->Mod_penyesuaian_stok->get($id);
        if ($penyesuaian) {
            // Hitung perubahan stok yang harus direversal
            $qty_awal = (float)$penyesuaian->qty_awal; //0
            $qty_baru = (float)$penyesuaian->qty_baru; //100
            $qty_selisih = $qty_baru - $qty_awal; //100
            $id_barang = $penyesuaian->id_barang; //1
            $id_gudang = $penyesuaian->id_gudang; //1
            
            // Catat reversal di stok_movement (pakai qty_in/qty_out sesuai struktur tabel)
            $data_movement = array(
                'tanggal' => date('Y-m-d H:i:s'),
                'id_barang' => $id_barang,
                'id_gudang' => $id_gudang,
                'tipe_transaksi' => 'reversal_penyesuaian',
                'qty_in' => $qty_selisih < 0 ? abs($qty_selisih) : 0,
                'qty_out' => $qty_selisih > 0 ? abs($qty_selisih) : 0,
                'keterangan' => 'Reversal penghapusan penyesuaian stok',
                'ref_transaksi' => $penyesuaian->kode_penyesuaian,
                'user_input' => 'user_' . $this->session->userdata('id_user')
            );
            $this->db->insert('stok_movement', $data_movement);
        }
        $this->Mod_penyesuaian_stok->delete($id);
        echo json_encode(array("status" => TRUE));
    }

    public function generate_kode()
    {
        $kode = $this->Mod_penyesuaian_stok->generate_kode();
        echo json_encode(array('kode' => $kode));
    }

    // Get detail barang via AJAX
    public function get_barang_detail()
    {
        $id_barang = $this->input->post('id_barang');
        $data = $this->Mod_penyesuaian_stok->get_barang_detail($id_barang);
        echo json_encode($data);
    }

    // Get detail gudang via AJAX
    public function get_gudang_detail()
    {
        $id_gudang = $this->input->post('id_gudang');
        $data = $this->Mod_penyesuaian_stok->get_gudang_detail($id_gudang);
        echo json_encode($data);
    }

    // Get stok terakhir barang di gudang tertentu
    public function get_stok_terakhir()
    {
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');

        if ($id_barang && $id_gudang) {
            $qty_terakhir = $this->Mod_penyesuaian_stok->get_stok_terakhir($id_barang, $id_gudang);
            echo json_encode(array('qty_terakhir' => $qty_terakhir));
        } else {
            echo json_encode(array('qty_terakhir' => 0));
        }
    }

    // Get stok movement history
    public function get_stok_movement()
    {
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $limit = $this->input->post('limit') ?: 10;

        $data = $this->Mod_penyesuaian_stok->get_stok_movement($id_barang, $id_gudang, $limit);
        echo json_encode($data);
    }

    // Get stok summary untuk modal info
    public function get_stok_info()
    {
        $id_barang = $this->input->post('id_barang');

        if ($id_barang) {
            $stok_by_gudang = $this->Mod_penyesuaian_stok->get_stok_by_barang($id_barang);
            $movement_history = $this->Mod_penyesuaian_stok->get_stok_movement($id_barang, null, 5);

            echo json_encode(array(
                'stok_by_gudang' => $stok_by_gudang,
                'movement_history' => $movement_history
            ));
        } else {
            echo json_encode(array(
                'stok_by_gudang' => array(),
                'movement_history' => array()
            ));
        }
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        $kode = $this->input->post('kode_penyesuaian');
        $id = $this->input->post('id');

        // Validasi kode penyesuaian
        if (empty($kode)) {
            // Kode boleh kosong, akan di-generate otomatis
        } else {
            if (!preg_match('/^PST-\d{8}-\d{4}$/', $kode)) {
                $data['inputerror'][] = 'kode_penyesuaian';
                $data['error_string'][] = 'Format kode harus PST-YYYYMMDD-XXXX, contoh: PST-20250627-0001.';
                $data['status'] = FALSE;
            } else if ($this->Mod_penyesuaian_stok->check_kode_exists($kode, $id)) {
                $data['inputerror'][] = 'kode_penyesuaian';
                $data['error_string'][] = 'Kode penyesuaian sudah ada';
                $data['status'] = FALSE;
            }
        }

        // Validasi tanggal
        if (empty($this->input->post('tanggal'))) {
            $data['inputerror'][] = 'tanggal';
            $data['error_string'][] = 'Tanggal wajib diisi';
            $data['status'] = FALSE;
        }

        // Validasi barang
        if (empty($this->input->post('id_barang'))) {
            $data['inputerror'][] = 'id_barang';
            $data['error_string'][] = 'Barang wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi gudang
        if (empty($this->input->post('id_gudang'))) {
            $data['inputerror'][] = 'id_gudang';
            $data['error_string'][] = 'Gudang wajib dipilih';
            $data['status'] = FALSE;
        }

        // Validasi qty awal
        $qty_awal = $this->input->post('qty_awal');
        if (empty($qty_awal) && $qty_awal !== '0') {
            $data['inputerror'][] = 'qty_awal';
            $data['error_string'][] = 'Qty awal wajib diisi';
            $data['status'] = FALSE;
        } else if (!is_numeric($qty_awal) || $qty_awal < 0) {
            $data['inputerror'][] = 'qty_awal';
            $data['error_string'][] = 'Qty awal harus berupa angka positif';
            $data['status'] = FALSE;
        }

        // Validasi qty baru
        $qty_baru = $this->input->post('qty_baru');
        if (empty($qty_baru) && $qty_baru !== '0') {
            $data['inputerror'][] = 'qty_baru';
            $data['error_string'][] = 'Qty baru wajib diisi';
            $data['status'] = FALSE;
        } else if (!is_numeric($qty_baru) || $qty_baru < 0) {
            $data['inputerror'][] = 'qty_baru';
            $data['error_string'][] = 'Qty baru harus berupa angka positif';
            $data['status'] = FALSE;
        }

        // Validasi alasan
        if (empty($this->input->post('alasan'))) {
            $data['inputerror'][] = 'alasan';
            $data['error_string'][] = 'Alasan penyesuaian wajib diisi';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }
}
