<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AkunDefault extends MY_Controller {

    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_akun_default');
    }

    public function index()
    {
        $this->template->load('layoutbackend', 'accounting/akun_default');
    }

    public function ajax_list()
    {
        $list = $this->Mod_akun_default->get_datatables();
        $data = array();
        $no = $_POST['start'];
        
        foreach ($list as $setting) {
            $no++;
            $row = array();
            $row[] = $setting->nama_setting;
            $row[] = $setting->kode_akun ?: '-';
            $row[] = $setting->nama_akun_chart ?: '-';
            $row[] = $setting->keterangan ?: '-';
            
            // Action buttons
            $action = '';
            $action .= '<a class="btn btn-xs btn-outline-primary" href="javascript:void(0)" title="Edit" onclick="edit_setting('.$setting->id.')"><i class="fas fa-edit"></i></a> ';
            $action .= '<a class="btn btn-xs btn-outline-danger" href="javascript:void(0)" title="Delete" onclick="delete_setting('.$setting->id.')"><i class="fas fa-trash"></i></a>';
            $row[] = $action;
            
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_akun_default->count_all(),
            "recordsFiltered" => $this->Mod_akun_default->count_filtered(),
            "data" => $data,
        );
        
        echo json_encode($output);
    }

    public function ajax_edit($id)
    {
        $data = $this->Mod_akun_default->get_by_id($id);
        echo json_encode($data);
    }

    public function ajax_add()
    {
        $this->_validate();
        
        $data = array(
            'nama_setting' => strtoupper($this->input->post('nama_setting')),
            'id_akun' => $this->input->post('id_akun'),
            'keterangan' => $this->input->post('keterangan')
        );
        
        $insert = $this->Mod_akun_default->save($data);
        
        if($insert) {
            echo json_encode(array("status" => TRUE, "message" => "Setting akun default berhasil ditambahkan"));
        } else {
            echo json_encode(array("status" => FALSE, "message" => "Gagal menambahkan setting akun default"));
        }
    }

    public function ajax_update()
    {
        $this->_validate();
        
        $id = $this->input->post('id');
        $data = array(
            'nama_setting' => strtoupper($this->input->post('nama_setting')),
            'id_akun' => $this->input->post('id_akun'),
            'keterangan' => $this->input->post('keterangan')
        );
        
        $update = $this->Mod_akun_default->update(array('id' => $id), $data);
        
        if($update) {
            echo json_encode(array("status" => TRUE, "message" => "Setting akun default berhasil diperbarui"));
        } else {
            echo json_encode(array("status" => FALSE, "message" => "Gagal memperbarui setting akun default"));
        }
    }

    public function ajax_delete($id)
    {
        $delete = $this->Mod_akun_default->delete_by_id($id);
        
        if($delete) {
            echo json_encode(array("status" => TRUE, "message" => "Setting akun default berhasil dihapus"));
        } else {
            echo json_encode(array("status" => FALSE, "message" => "Gagal menghapus setting akun default"));
        }
    }

    public function get_chart_of_accounts()
    {
        $accounts = $this->Mod_akun_default->get_all_chart_of_accounts();
        echo json_encode($accounts);
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        // Validate nama_setting
        if($this->input->post('nama_setting') == '') {
            $data['inputerror'][] = 'nama_setting';
            $data['error_string'][] = 'Nama setting wajib diisi';
            $data['status'] = FALSE;
        } else {
            $nama_setting = strtoupper($this->input->post('nama_setting'));
            $id = $this->input->post('id');
            
            if($this->Mod_akun_default->check_nama_setting_exists($nama_setting, $id)) {
                $data['inputerror'][] = 'nama_setting';
                $data['error_string'][] = 'Nama setting sudah ada';
                $data['status'] = FALSE;
            }
        }

        // Validate id_akun
        if($this->input->post('id_akun') == '') {
            $data['inputerror'][] = 'id_akun';
            $data['error_string'][] = 'Akun wajib dipilih';
            $data['status'] = FALSE;
        }

        if($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    public function export_excel()
    {
        $this->load->library('phpexcel');
        
        $objPHPExcel = new PHPExcel();
        
        // Set properties
        $objPHPExcel->getProperties()->setCreator("Toko Elektronik System")
                                     ->setLastModifiedBy("Toko Elektronik System")
                                     ->setTitle("Data Akun Default")
                                     ->setSubject("Data Akun Default")
                                     ->setDescription("Data Akun Default Export");

        // Set active sheet
        $objPHPExcel->setActiveSheetIndex(0);
        $sheet = $objPHPExcel->getActiveSheet();
        
        // Set headers
        $sheet->setCellValue('A1', 'No');
        $sheet->setCellValue('B1', 'Nama Setting');
        $sheet->setCellValue('C1', 'Kode Akun');
        $sheet->setCellValue('D1', 'Nama Akun');
        $sheet->setCellValue('E1', 'Keterangan');
        
        // Style header
        $headerStyle = array(
            'font' => array('bold' => true),
            'alignment' => array('horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER),
            'fill' => array('type' => PHPExcel_Style_Fill::FILL_SOLID, 'color' => array('rgb' => 'E0E0E0'))
        );
        $sheet->getStyle('A1:E1')->applyFromArray($headerStyle);
        
        // Get data
        $data = $this->Mod_akun_default->get_all();
        $row = 2;
        $no = 1;
        
        foreach($data as $item) {
            $sheet->setCellValue('A'.$row, $no);
            $sheet->setCellValue('B'.$row, $item->nama_setting);
            $sheet->setCellValue('C'.$row, $item->kode_akun ?: '-');
            $sheet->setCellValue('D'.$row, $item->nama_akun_chart ?: '-');
            $sheet->setCellValue('E'.$row, $item->keterangan ?: '-');
            $row++;
            $no++;
        }
        
        // Auto size columns
        foreach(range('A','E') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }
        
        // Set sheet title
        $sheet->setTitle('Akun Default');
        
        // Output
        $filename = 'Data_Akun_Default_' . date('Y-m-d_H-i-s') . '.xlsx';
        
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="'.$filename.'"');
        header('Cache-Control: max-age=0');
        
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $objWriter->save('php://output');
    }
}
