<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Gudang extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        // Load required models
        $this->load->model('Mod_gudang');
    }

    public function index()
    {
        $link = 'gudang';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $this->template->load('layoutbackend', 'gudang/gudang', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $this->set_resource_limits();
        $list = $this->Mod_gudang->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->kode_gudang ?? '-';
            $sub_array[] = $row->nama_gudang;
            $sub_array[] = $row->alamat ?? '-';
            $sub_array[] = $row->penanggung_jawab ?? '-';
            $sub_array[] = $row->no_telp ?? '-';
            $sub_array[] = $row->email ?? '-';
            $sub_array[] = ($row->aktif == 1) ? '<span class="badge badge-success">Aktif</span>' : '<span class="badge badge-danger">Tidak Aktif</span>';
            $sub_array[] = '<a class="btn btn-sm btn-outline-primary" href="javascript:void(0)" title="Edit" onclick="edit(' . "'" . $row->id . "'" . ')"><i class="fas fa-edit"></i></a>
                  <a class="btn btn-sm btn-outline-danger" href="javascript:void(0)" title="Hapus" onclick="hapus(' . "'" . $row->id . "'" . ')"><i class="fas fa-trash"></i></a>';

            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_gudang->count_all(),
            "recordsFiltered" => $this->Mod_gudang->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function form_input()
    {
        $this->load->view('gudang/form_input');
    }
    public function insert()
    {
        $this->_validate();

        // Generate kode otomatis jika kosong
        $kode_gudang = $this->input->post('kode_gudang');
        if (empty($kode_gudang)) {
            $kode_gudang = $this->Mod_gudang->generate_kode();
        }

        $save = array(
            'kode_gudang' => $kode_gudang,
            'nama_gudang' => $this->input->post('nama_gudang'),
            'alamat' => $this->input->post('alamat'),
            'penanggung_jawab' => $this->input->post('penanggung_jawab'),
            'no_telp' => $this->input->post('no_telp'),
            'email' => $this->input->post('email'),
            'keterangan' => $this->input->post('keterangan'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );
        $this->Mod_gudang->insert('gudang', $save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $save = array(
            'kode_gudang' => $this->input->post('kode_gudang'),
            'nama_gudang' => $this->input->post('nama_gudang'),
            'alamat' => $this->input->post('alamat'),
            'penanggung_jawab' => $this->input->post('penanggung_jawab'),
            'no_telp' => $this->input->post('no_telp'),
            'email' => $this->input->post('email'),
            'keterangan' => $this->input->post('keterangan'),
            'aktif' => $this->input->post('aktif') ? 1 : 0,
        );
        $this->Mod_gudang->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_gudang->get($id);
        echo json_encode($data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_gudang->delete($id, 'gudang');
        echo json_encode(array("status" => TRUE));
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        if ($this->input->post('nama_gudang') == '') {
            $data['inputerror'][] = 'nama_gudang';
            $data['error_string'][] = 'Nama Gudang Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        // Validasi kode gudang jika diisi
        $kode_gudang = $this->input->post('kode_gudang');
        $id = $this->input->post('id');
        if (!empty($kode_gudang)) {
            if ($this->Mod_gudang->check_kode_exists($kode_gudang, $id)) {
                $data['inputerror'][] = 'kode_gudang';
                $data['error_string'][] = 'Kode Gudang sudah ada';
                $data['status'] = FALSE;
            }
        }

        // Validasi nomor telepon jika diisi
        $no_telp = $this->input->post('no_telp');
        if (!empty($no_telp) && !$this->_validate_phone($no_telp)) {
            $data['inputerror'][] = 'no_telp';
            $data['error_string'][] = 'Format No Telepon Tidak Valid (minimal 10-15 digit angka)';
            $data['status'] = FALSE;
        }

        // Validasi email jika diisi
        $email = $this->input->post('email');
        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $data['inputerror'][] = 'email';
            $data['error_string'][] = 'Format Email Tidak Valid';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    // Method untuk validasi format nomor telepon
    private function _validate_phone($phone)
    {
        // Hapus semua karakter non-digit
        $phone_clean = preg_replace('/[^0-9]/', '', $phone);

        // Validasi panjang nomor telepon (10-15 digit)
        if (strlen($phone_clean) < 10 || strlen($phone_clean) > 15) {
            return false;
        }

        // Validasi format Indonesia (dimulai dengan 0, 62, atau +62)
        if (preg_match('/^(\+?62|0)[0-9]{8,13}$/', $phone_clean)) {
            return true;
        }

        // Validasi format internasional umum
        if (preg_match('/^[0-9]{10,15}$/', $phone_clean)) {
            return true;
        }

        return false;
    }

    // Method untuk generate kode otomatis via AJAX
    public function generate_kode()
    {
        $kode = $this->Mod_gudang->generate_kode();
        echo json_encode(array('kode' => $kode));
    }

    // Method untuk toggle status aktif
    public function toggle_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        $result = $this->Mod_gudang->update_status($id, $status);
        echo json_encode(array("status" => $result));
    }
}
