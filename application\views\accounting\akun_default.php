<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fas fa-cog text-blue"></i> Aku<PERSON></h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add_setting()" title="Add Setting"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="tbl_akun_default" class="table table-bordered table-striped table-hover w-100" style="width:100%">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nama Setting</th>
                                    <th>Kode <PERSON>n</th>
                                    <th><PERSON>a <PERSON></th>
                                    <th>Keterangan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>

<!-- Modal Form -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-cog"></i> Form Akun Default</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="#" id="form" class="form-horizontal">
                <div class="modal-body">
                    <input type="hidden" value="" name="id"/>
                    
                    <div class="form-group row">
                        <label class="control-label col-md-3">Nama Setting <span class="text-red">*</span></label>
                        <div class="col-md-9">
                            <input name="nama_setting" placeholder="Nama Setting (contoh: KAS, BANK, PIUTANG_DAGANG)" class="form-control" type="text" style="text-transform: uppercase;">
                            <span class="help-block"></span>
                        </div>
                    </div>
                    
                    <div class="form-group row">
                        <label class="control-label col-md-3">Akun <span class="text-red">*</span></label>
                        <div class="col-md-9">
                            <select name="id_akun" class="form-control select2" style="width: 100%;">
                                <option value="">-- Pilih Akun --</option>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>
                    
                    <div class="form-group row">
                        <label class="control-label col-md-3">Keterangan</label>
                        <div class="col-md-9">
                            <textarea name="keterangan" placeholder="Keterangan (opsional)" class="form-control" rows="3"></textarea>
                            <span class="help-block"></span>
                        </div>
                    </div>
                    
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">Save</button>
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                </div>
            </form>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- End Modal Form -->

<script type="text/javascript">
var save_method;
var table;
var base_url = '<?php echo base_url();?>';

$(document).ready(function() {
    table = $('#tbl_akun_default').DataTable({
        "autoWidth": false,
        "processing": true,
        "serverSide": true,
        "order": [],
        "responsive": true,
        "scrollX": true,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        "pageLength": 25,
        "ajax": {
            "url": base_url + "akundefault/ajax_list",
            "type": "POST"
        },
        "columnDefs": [{
            "targets": [-1],
            "orderable": false,
        }]
    });

    // Universal handler to clear errors on input change
    $("#form").on("change", "input, textarea, select", function() {
        $(this).closest('.form-group').removeClass('has-error');
        $(this).closest('.form-group').find('.help-block').empty();
    });

    $(document).on('input', 'input[name="nama_setting"]', function() {
        this.value = this.value.replace(/[^A-Z0-9_]/g, '').toUpperCase();
    });
});

function add_setting() {
    save_method = 'add';
    $('#form')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();
    $('.modal-title').text('Add Akun Default');
    
    // Load accounts and then show the modal
    load_accounts_and_show_modal(null);
}

function edit_setting(id) {
    save_method = 'update';
    $('#form')[0].reset();
    $('.form-group').removeClass('has-error');
    $('.help-block').empty();
    $('.modal-title').text('Edit Akun Default');
    
    // Get setting data first
    $.ajax({
        url: base_url + "akundefault/ajax_edit/" + id,
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            $('[name="id"]').val(data.id);
            $('[name="nama_setting"]').val(data.nama_setting);
            $('[name="keterangan"]').val(data.keterangan);
            
            // Now, load accounts with the correct value, then show the modal
            load_accounts_and_show_modal(data.id_akun);
        },
        error: function() {
            alert('Error get data from ajax');
        }
    });
}

function load_accounts_and_show_modal(selectedValue) {
    var select = $('select[name="id_akun"]');

    // Destroy any existing Select2 to prevent conflicts
    if (select.hasClass('select2-hidden-accessible')) {
        select.select2('destroy');
    }

    $.ajax({
        url: base_url + "akundefault/get_chart_of_accounts",
        type: "GET",
        dataType: "JSON",
        success: function(data) {
            // Populate the select with new data
            select.empty().append('<option value="">-- Pilih Akun --</option>');
            $.each(data, function(index, item) {
                select.append('<option value="' + item.id + '">' + item.kode_akun + ' - ' + item.nama_akun + '</option>');
            });

            // Set the selected value if one is provided
            if (selectedValue) {
                select.val(selectedValue);
            }

            // Initialize Select2
            select.select2({
                dropdownParent: $('#modal_form'),
                placeholder: "-- Pilih Akun --",
                allowClear: true,
                width: '100%'
            });

            // Finally, show the modal
            $('#modal_form').modal('show');
        },
        error: function() {
            alert('Error loading chart of accounts');
        }
    });
}


function reload_table() {
    table.ajax.reload(null, false);
}

function save() {
    $('#btnSave').text('saving...').attr('disabled', true);
    var url = save_method == 'add' ? base_url + "akundefault/ajax_add" : base_url + "akundefault/ajax_update";

    $.ajax({
        url: url,
        type: "POST",
        data: $('#form').serialize(),
        dataType: "JSON",
        success: function(data) {
            if(data.status) {
                $('#modal_form').modal('hide');
                reload_table();
                Swal.fire({
                    title: 'Berhasil',
                    text: data.message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                for (var i = 0; i < data.inputerror.length; i++) {
                    var el = $('[name="' + data.inputerror[i] + '"]');
                    el.closest('.form-group').addClass('has-error');
                    el.nextAll('.help-block').first().text(data.error_string[i]);
                }
            }
            $('#btnSave').text('save').attr('disabled', false);
        },
        error: function() {
            alert('Error adding / update data');
            $('#btnSave').text('save').attr('disabled', false);
        }
    });
}

function delete_setting(id) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: "Data akan dihapus permanen!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: base_url + "akundefault/ajax_delete/" + id,
                type: "POST",
                dataType: "JSON",
                success: function(data) {
                    if(data.status) {
                        reload_table();
                        Swal.fire({
                            title: 'Berhasil',
                            text: data.message,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire('Gagal', data.message, 'error');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Error deleting data', 'error');
                }
            });
        }
    });
}
</script>