<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Supplier extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_supplier');
    }

    public function index()
    {
        $link = 'supplier';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $this->template->load('layoutbackend', 'supplier/supplier', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        $this->set_resource_limits();
        $list = $this->Mod_supplier->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->kode ?? '-';
            $sub_array[] = $row->nama;
            $sub_array[] = $row->no_telepon;
            $sub_array[] = $row->email ?? '-';
            $sub_array[] = $row->nama_pic ?? '-';
            $sub_array[] = ($row->status_aktif == 1) ? '<span class="badge badge-success">Aktif</span>' : '<span class="badge badge-danger">Tidak Aktif</span>';

            $sub_array[] = "<a class=\"btn btn-xs btn-outline-primary edit\" href=\"javascript:void(0)\" title=\"Edit\" onclick=\"edit('$row->id')\"><i class=\"fas fa-edit\"></i></a>
                             <a class=\"btn btn-xs btn-outline-danger delete\" href=\"javascript:void(0)\" title=\"Delete\" onclick=\"hapus('$row->id')\"><i class=\"fas fa-trash\"></i></a>";
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_supplier->count_all(),
            "recordsFiltered" => $this->Mod_supplier->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function form_input()
    {
        $this->load->view('supplier/form_input');
    }
    public function insert()
    {
        $this->_validate();

        // Generate kode otomatis jika tidak diisi
        $kode = $this->input->post('kode');
        if (empty($kode)) {
            $kode = $this->Mod_supplier->generate_kode();
        }

        $save = array(
            'kode' => $kode,
            'nama' => $this->input->post('nama'),
            'no_telepon' => $this->input->post('no_telepon'),
            'alamat' => $this->input->post('alamat'),
            'alamat_kirim' => $this->input->post('alamat_kirim'),
            'email' => $this->input->post('email'),
            'nama_pic' => $this->input->post('nama_pic'),
            'telepon_pic' => $this->input->post('telepon_pic'),
            'npwp' => $this->input->post('npwp'),
            'no_ktp' => $this->input->post('no_ktp'),
            'is_pkp' => $this->input->post('is_pkp') ? 1 : 0,
            'status_aktif' => $this->input->post('status_aktif') ? 1 : 0,
        );
        $this->Mod_supplier->insert('supplier', $save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');
        $save = array(
            'nama' => $this->input->post('nama'),
            'no_telepon' => $this->input->post('no_telepon'),
            'alamat' => $this->input->post('alamat'),
            'alamat_kirim' => $this->input->post('alamat_kirim'),
            'email' => $this->input->post('email'),
            'nama_pic' => $this->input->post('nama_pic'),
            'telepon_pic' => $this->input->post('telepon_pic'),
            'npwp' => $this->input->post('npwp'),
            'no_ktp' => $this->input->post('no_ktp'),
            'is_pkp' => $this->input->post('is_pkp') ? 1 : 0,
            'status_aktif' => $this->input->post('status_aktif') ? 1 : 0,
        );
        $this->Mod_supplier->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_supplier->get($id);
        echo json_encode($data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_supplier->delete($id, 'supplier');
        echo json_encode(array("status" => TRUE));
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        // Validasi kode supplier
        $kode = $this->input->post('kode');
        $id = $this->input->post('id');
        if (!empty($kode)) {
            if ($this->Mod_supplier->check_kode_exists($kode, $id)) {
                $data['inputerror'][] = 'kode';
                $data['error_string'][] = 'Kode Supplier Sudah Ada';
                $data['status'] = FALSE;
            }
        }

        // Validasi nama (wajib)
        if ($this->input->post('nama') == '') {
            $data['inputerror'][] = 'nama';
            $data['error_string'][] = 'Nama Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        // Validasi no telepon (wajib)
        $no_telepon = $this->input->post('no_telepon');
        if ($no_telepon == '') {
            $data['inputerror'][] = 'no_telepon';
            $data['error_string'][] = 'No Telepon Tidak Boleh Kosong';
            $data['status'] = FALSE;
        } elseif (!$this->_validate_phone($no_telepon)) {
            $data['inputerror'][] = 'no_telepon';
            $data['error_string'][] = 'Format No Telepon Tidak Valid (minimal 10-15 digit angka)';
            $data['status'] = FALSE;
        }

        // Validasi telepon PIC jika diisi
        $telepon_pic = $this->input->post('telepon_pic');
        if (!empty($telepon_pic) && !$this->_validate_phone($telepon_pic)) {
            $data['inputerror'][] = 'telepon_pic';
            $data['error_string'][] = 'Format Telepon PIC Tidak Valid (minimal 10-15 digit angka)';
            $data['status'] = FALSE;
        }

        // Validasi email format jika diisi
        $email = $this->input->post('email');
        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $data['inputerror'][] = 'email';
            $data['error_string'][] = 'Format Email Tidak Valid';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    // Method untuk validasi format nomor telepon
    private function _validate_phone($phone)
    {
        // Hapus semua karakter non-digit
        $phone_clean = preg_replace('/[^0-9]/', '', $phone);

        // Validasi panjang nomor telepon (10-15 digit)
        if (strlen($phone_clean) < 10 || strlen($phone_clean) > 15) {
            return false;
        }

        // Validasi format Indonesia (dimulai dengan 0, 62, atau +62)
        if (preg_match('/^(\+?62|0)[0-9]{8,13}$/', $phone_clean)) {
            return true;
        }

        // Validasi format internasional umum
        if (preg_match('/^[0-9]{10,15}$/', $phone_clean)) {
            return true;
        }

        return false;
    }

    // Method untuk generate kode otomatis via AJAX
    public function generate_kode()
    {
        $kode = $this->Mod_supplier->generate_kode();
        echo json_encode(array('kode' => $kode));
    }

    // Method untuk toggle status aktif
    public function toggle_status()
    {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
        $result = $this->Mod_supplier->update_status($id, $status);
        echo json_encode(array("status" => $result));
    }
}
