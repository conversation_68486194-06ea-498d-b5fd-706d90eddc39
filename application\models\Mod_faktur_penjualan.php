<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Faktur Penjualan
 * Mengelola data faktur penjualan dan detailnya
 */
class Mod_faktur_penjualan extends CI_Model
{
    var $table = 'faktur_penjualan';
    var $column_order = array(null, 'nomor_faktur', 'tanggal_faktur', 'nomor_pengiriman', 'nama', 'status', 'total_item', 'total_qty', 'total_faktur', 'status_pembayaran', null);
    var $column_search = array('faktur_penjualan.nomor_faktur', 'pengiriman.nomor_pengiriman', 'pelanggan.nama', 'pelanggan.kode');
    var $order = array('faktur_penjualan.id' => 'desc');

    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('faktur_penjualan.*, pengiriman.nomor_pengiriman, pelanggan.nama as nama_pelanggan, pelanggan.kode as kode_pelanggan, pesanan.nomor_pesanan');
        $this->db->from($this->table);
        $this->db->join('pengiriman', 'pengiriman.id = faktur_penjualan.id_pengiriman', 'left');
        $this->db->join('pelanggan', 'pelanggan.id = faktur_penjualan.id_pelanggan', 'left');
        $this->db->join('pesanan', 'pesanan.id = faktur_penjualan.id_pesanan', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }
                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    public function get_by_id($id)
    {
        $this->db->select('faktur_penjualan.*, pengiriman.nomor_pengiriman, pengiriman.tanggal_pengiriman, pengiriman.alamat_pengiriman, pengiriman.penerima, pengiriman.tanggal_diterima, pelanggan.nama as nama_pelanggan, pelanggan.kode as kode_pelanggan, pelanggan.alamat as alamat_pelanggan, pelanggan.no_telepon as telepon, pelanggan.email, pesanan.nomor_pesanan, pesanan.tanggal_pesanan');
        $this->db->from($this->table);
        $this->db->join('pengiriman', 'pengiriman.id = faktur_penjualan.id_pengiriman', 'left');
        $this->db->join('pelanggan', 'pelanggan.id = faktur_penjualan.id_pelanggan', 'left');
        $this->db->join('pesanan', 'pesanan.id = faktur_penjualan.id_pesanan', 'left');
        $this->db->where('faktur_penjualan.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_detail_by_faktur_id($id_faktur)
    {
        $this->db->select('
            faktur_penjualan_detail.*, 
            barang.kode_barang, 
            barang.nama_barang, 
            barang.merk, 
            barang.tipe, 
            barang.jenis_pajak_id,
            satuan.nama_satuan,
            jp.nama_pajak,
            jp.tarif_persen as pajak_barang_persen
        ');
        $this->db->from('faktur_penjualan_detail');
        $this->db->join('barang', 'barang.id = faktur_penjualan_detail.id_barang', 'left');
        $this->db->join('satuan', 'satuan.id = barang.satuan_id', 'left');
        $this->db->join('jenis_pajak jp', 'barang.jenis_pajak_id = jp.id', 'left');
        $this->db->where('faktur_penjualan_detail.id_faktur_penjualan', $id_faktur);
        $query = $this->db->get();
        return $query->result();
    }

    public function get_pembayaran_by_faktur_id($id_faktur)
    {
        $this->db->select('*');
        $this->db->from('faktur_penjualan_pembayaran');
        $this->db->where('id_faktur_penjualan', $id_faktur);
        $this->db->order_by('tanggal_pembayaran', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_pengiriman_diterima()
    {
        $this->db->select('pengiriman.*, pelanggan.nama as nama_pelanggan, pelanggan.kode as kode_pelanggan, pesanan.nomor_pesanan');
        $this->db->from('pengiriman');
        $this->db->join('pelanggan', 'pelanggan.id = pengiriman.id_pelanggan', 'left');
        $this->db->join('pesanan', 'pesanan.id = pengiriman.id_pesanan', 'left');
        $this->db->where('pengiriman.status', 'delivered');
        
        // Exclude pengiriman yang sudah memiliki faktur
        $this->db->where('NOT EXISTS (SELECT 1 FROM faktur_penjualan WHERE faktur_penjualan.id_pengiriman = pengiriman.id)', NULL, FALSE);
        
        $this->db->order_by('pengiriman.tanggal_diterima', 'desc');
        $query = $this->db->get();
        return $query->result();
    }

    public function get_pengiriman_by_id($id)
    {
        $this->db->select('pengiriman.*, pelanggan.nama as nama_pelanggan, pelanggan.kode as kode_pelanggan, pelanggan.alamat as alamat_pelanggan, pelanggan.no_telepon as telepon, pelanggan.email, pesanan.nomor_pesanan, pesanan.tanggal_pesanan');
        $this->db->from('pengiriman');
        $this->db->join('pelanggan', 'pelanggan.id = pengiriman.id_pelanggan', 'left');
        $this->db->join('pesanan', 'pesanan.id = pengiriman.id_pesanan', 'left');
        $this->db->where('pengiriman.id', $id);
        $query = $this->db->get();
        return $query->row();
    }

    public function get_pengiriman_detail($id_pengiriman)
    {
        $this->db->select('
            pengiriman_detail.*, 
            barang.kode_barang, 
            barang.nama_barang, 
            barang.merk, 
            barang.tipe, 
            barang.harga_jual, 
            barang.jenis_pajak_id,
            satuan.nama_satuan, 
            gudang.nama_gudang,
            jp.nama_pajak,
            jp.tarif_persen as pajak_persen
        ');
        $this->db->from('pengiriman_detail');
        $this->db->join('barang', 'barang.id = pengiriman_detail.id_barang', 'left');
        $this->db->join('satuan', 'satuan.id = barang.satuan_id', 'left');
        $this->db->join('gudang', 'gudang.id = pengiriman_detail.id_gudang', 'left');
        $this->db->join('jenis_pajak jp', 'barang.jenis_pajak_id = jp.id', 'left');
        $this->db->where('pengiriman_detail.id_pengiriman', $id_pengiriman);
        $query = $this->db->get();
        return $query->result();
    }


    public function get_barang_aktif()
    {
        $this->db->select('
            barang.id, 
            barang.kode_barang, 
            barang.nama_barang, 
            barang.merk, 
            barang.tipe, 
            barang.harga_jual, 
            barang.jenis_pajak_id,
            satuan.nama_satuan,
            jp.nama_pajak,
            jp.tarif_persen as pajak_persen
        ');
        $this->db->from('barang');
        $this->db->join('satuan', 'satuan.id = barang.satuan_id', 'left');
        $this->db->join('jenis_pajak jp', 'barang.jenis_pajak_id = jp.id', 'left');
        $this->db->where('barang.aktif', 1);
        $this->db->order_by('barang.nama_barang', 'asc');
        $query = $this->db->get();
        return $query->result();
    }

    // Generate nomor faktur - Format: FPJ-YYYYMMDD-XXXX (Faktur Penjualan)
    public function generate_nomor_faktur()
    {
        $prefix = 'FPJ';
        $date = date('Ymd');
        
        $this->db->select('nomor_faktur');
        $this->db->from('faktur_penjualan');
        $this->db->like('nomor_faktur', $prefix . '-' . $date, 'after');
        $this->db->order_by('nomor_faktur', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_number = $query->row()->nomor_faktur;
            $last_sequence = (int)substr($last_number, -4);
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . '-' . $date . '-' . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    public function insert($table, $data)
    {
        $this->db->insert($table, $data);
        return $this->db->insert_id();
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    public function delete($id, $table = null)
    {
        if ($table === null) {
            $table = $this->table;
        }
        $this->db->where('id', $id);
        return $this->db->delete($table);
    }

    public function save_detail($data)
    {
        return $this->db->insert('faktur_penjualan_detail', $data);
    }

    public function update_detail($where, $data)
    {
        $this->db->where($where);
        return $this->db->update('faktur_penjualan_detail', $data);
    }

    public function delete_detail($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete('faktur_penjualan_detail');
    }

    public function delete_detail_by_faktur($id_faktur)
    {
        $this->db->where('id_faktur_penjualan', $id_faktur);
        return $this->db->delete('faktur_penjualan_detail');
    }

    public function update_total_faktur($id_faktur)
    {
        // Get all details for this faktur
        $this->db->select('SUM(qty) as total_qty, COUNT(id) as total_item, SUM(subtotal) as subtotal, SUM(diskon_nilai) as total_diskon, SUM(pajak_nilai) as total_pajak, SUM(total) as total_faktur');
        $this->db->from('faktur_penjualan_detail');
        $this->db->where('id_faktur_penjualan', $id_faktur);
        $query = $this->db->get();
        $result = $query->row();
        
        // Update faktur with totals
        $data = array(
            'total_item' => $result->total_item ?? 0,
            'total_qty' => $result->total_qty ?? 0,
            'subtotal' => $result->subtotal ?? 0,
            'diskon' => $result->total_diskon ?? 0,
            'pajak' => $result->total_pajak ?? 0,
            'total_faktur' => $result->total_faktur ?? 0,
            'updated_by' => $this->session->userdata('nama_user'),
        );
        
        $this->db->where('id', $id_faktur);
        return $this->db->update('faktur_penjualan', $data);
    }

    public function insert_pembayaran($data)
    {
        $this->db->insert('faktur_penjualan_pembayaran', $data);
        $insert_id = $this->db->insert_id();
        
        if ($insert_id) {
            // Update status pembayaran faktur
            $this->update_status_pembayaran($data['id_faktur_penjualan']);
        }
        
        return $insert_id;
    }

    public function delete_pembayaran($id)
    {
        // Get faktur_id before deleting
        $this->db->select('id_faktur_penjualan');
        $this->db->from('faktur_penjualan_pembayaran');
        $this->db->where('id', $id);
        $query = $this->db->get();
        $pembayaran = $query->row();
        
        if ($pembayaran) {
            $id_faktur = $pembayaran->id_faktur_penjualan;
            
            // Delete the payment
            $this->db->where('id', $id);
            $result = $this->db->delete('faktur_penjualan_pembayaran');
            
            if ($result) {
                // Update status pembayaran faktur
                $this->update_status_pembayaran($id_faktur);
            }
            
            return $result;
        }
        
        return false;
    }

    public function update_status_pembayaran($id_faktur)
    {
        // Get faktur total
        $this->db->select('total_faktur');
        $this->db->from('faktur_penjualan');
        $this->db->where('id', $id_faktur);
        $query = $this->db->get();
        $faktur = $query->row();
        
        if (!$faktur) return false;
        
        // Get total payments
        $this->db->select('SUM(jumlah_pembayaran) as total_pembayaran');
        $this->db->from('faktur_penjualan_pembayaran');
        $this->db->where('id_faktur_penjualan', $id_faktur);
        $query = $this->db->get();
        $pembayaran = $query->row();
        
        $total_pembayaran = $pembayaran->total_pembayaran ?? 0;
        $status_pembayaran = 'belum_bayar';
        
        if ($total_pembayaran >= $faktur->total_faktur) {
            $status_pembayaran = 'lunas';
        } else if ($total_pembayaran > 0) {
            $status_pembayaran = 'sebagian';
        }
        
        // Update faktur status
        $this->db->where('id', $id_faktur);
        return $this->db->update('faktur_penjualan', array('status_pembayaran' => $status_pembayaran));
    }
    
    // Alias for backward compatibility
    public function save_pembayaran($data)
    {
        return $this->insert_pembayaran($data);
    }
    
    public function get_total_paid($id_faktur)
    {
        $this->db->select('SUM(jumlah_pembayaran) as total_paid');
        $this->db->from('faktur_penjualan_pembayaran');
        $this->db->where('id_faktur_penjualan', $id_faktur);
        $query = $this->db->get();
        $result = $query->row();
        
        return $result->total_paid ?? 0;
    }
}
