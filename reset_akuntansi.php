<?php
die("Admin only!");
// ##################################################################################
// # PERINGATAN KERAS!                                                              #
// # =================                                                              #
// # Skrip ini akan MENGHAPUS DATA JURNAL & LOG secara permanen.                    #
// # PASTIKAN ANDA SUDAH MEMBUAT BACKUP DATABASE SEBELUM MENJALANKAN SKRIP INI.     #
// # Setelah selesai digunakan, SEGERA HAPUS file ini dari server Anda.             #
// ##################################################################################

// Nonaktifkan batas waktu eksekusi
set_time_limit(0);
ini_set('memory_limit', '-1');

header('Content-Type: text/plain');
echo "Memulai Proses Reset Akuntansi...\n";
echo "--------------------------------------------------\n";

// --- 1. Membaca Konfigurasi Database CodeIgniter ---
$config_path = 'application/config/database.php';
if (!file_exists($config_path)) {
    die("GAGAL: File konfigurasi database tidak ditemukan di: " . $config_path);
}
if (!defined('BASEPATH')) define('BASEPATH', true);
if (!defined('ENVIRONMENT')) define('ENVIRONMENT', 'development');
$db = [];
include($config_path);

$db_host = $db['default']['hostname'];
$db_user = $db['default']['username'];
$db_pass = $db['default']['password'];
$db_name = $db['default']['database'];

echo "Mencoba terhubung ke database '{$db_name}' di host '{$db_host}'...\n";

// --- 2. Membuat Koneksi ke Database ---
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
if ($conn->connect_error) {
    die("KONEKSI GAGAL: " . $conn->connect_error);
}
echo "Koneksi database berhasil.\n\n";

// --- 3. Daftar Tabel ---
$tables_to_truncate = [
    'jurnal_detail',
    'jurnal_umum',
    'accounting_integration_log'
];

$tables_to_regenerate = [
    'pembelian'             => 'id_pembelian',
    'faktur_penjualan'      => 'id_faktur',
    'retur_pembelian'       => 'id_retur_pembelian',
    'retur_penjualan'       => 'id_retur_penjualan',
    'penerimaan_pembelian'  => 'id_penerimaan',
    'penyesuaian_stok'      => 'id_penyesuaian'
];

// --- 4. Proses TRUNCATE Tabel ---
echo "Tahap 1: Mengosongkan Tabel Akuntansi...\n";
$conn->query('SET FOREIGN_KEY_CHECKS=0');
echo "[INFO] Foreign key checks dinonaktifkan.\n";
foreach ($tables_to_truncate as $table) {
    $sql = "TRUNCATE TABLE `" . $table . "`";
    if ($conn->query($sql) === TRUE) {
        echo "[OK] Tabel '$table' berhasil dikosongkan.\n";
    } else {
        $check_table_sql = "SHOW TABLES LIKE '$table'";
        $result = $conn->query($check_table_sql);
        if ($result && $result->num_rows == 0) {
            echo "[WARN] Tabel '$table' tidak ditemukan, dilewati.\n";
        } else {
            echo "[ERROR] Gagal mengosongkan tabel '$table': (" . $conn->errno . ") " . $conn->error . "\n";
        }
    }
}
$conn->query('SET FOREIGN_KEY_CHECKS=1');
echo "[INFO] Foreign key checks diaktifkan kembali.\n";
echo "Proses pengosongan tabel selesai.\n\n";

// --- 5. Proses Regenerasi Log via Trigger ---
echo "Tahap 2: Regenerasi Log Transaksi...\n";
echo "Proses ini mungkin memakan waktu beberapa saat tergantung jumlah data.\n";
foreach ($tables_to_regenerate as $table => $primary_key) {
    echo "  - Memproses tabel `$table`... ";
    $result = $conn->query("SELECT COUNT(*) as total FROM `$table`");
    if ($result) {
        $row = $result->fetch_assoc();
        if ($row['total'] == 0) {
            echo "Tidak ada data, dilewati.\n";
            continue;
        }
        $update_query = "UPDATE `$table` SET `$primary_key` = `$primary_key`";
        if ($conn->query($update_query)) {
            $affected_rows = $conn->affected_rows;
            echo "$affected_rows baris diproses. [OK]\n";
        } else {
            echo "[GAGAL: " . $conn->error . "]\n";
        }
    } else {
        $check_table_sql = "SHOW TABLES LIKE '$table'";
        $result2 = $conn->query($check_table_sql);
        if ($result2 && $result2->num_rows == 0) {
            echo "[WARN] Tabel '$table' tidak ditemukan, dilewati.\n";
        } else {
            echo "[ERROR] Query gagal pada tabel '$table': (" . $conn->errno . ") " . $conn->error . "\n";
        }
    }
}
echo "\nProses regenerasi log selesai.\n";
echo "--------------------------------------------------\n";
echo "PROSES RESET SELESAI.\n";
echo "Silakan periksa kembali data di tabel `jurnal` dan `accounting_integration_log`.\n";

// --- 6. Tutup Koneksi ---
$conn->close();